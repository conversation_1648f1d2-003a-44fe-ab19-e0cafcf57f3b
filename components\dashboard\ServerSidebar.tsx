'use client';

import { useState } from 'react';
import { Plus, Hash } from 'lucide-react';
import { useApp } from '@/lib/contexts/AppContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { CreateServerDialog } from './CreateServerDialog';
import { JoinServerDialog } from './JoinServerDialog';
import { cn } from '@/lib/utils';

export function ServerSidebar() {
  const { state, actions } = useApp();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showJoinDialog, setShowJoinDialog] = useState(false);

  const handleServerClick = (serverId: string) => {
    actions.setCurrentServer(serverId);
  };

  return (
    <div className="flex flex-col items-center py-3 space-y-2 h-full">
      {/* Direct Messages / Home Button */}
      <div className="relative group">
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "w-12 h-12 rounded-full bg-gray-600 hover:bg-blue-600 hover:rounded-2xl transition-all duration-200",
            !state.currentServer && "bg-blue-600 rounded-2xl"
          )}
          onClick={() => {
            // Handle direct messages or home view
          }}
        >
          <Hash className="w-6 h-6" />
        </Button>
        <div className="absolute left-full ml-2 px-2 py-1 bg-black text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
          Direct Messages
        </div>
      </div>

      {/* Separator */}
      <div className="w-8 h-0.5 bg-gray-600 rounded-full" />

      {/* Server List */}
      <div className="flex flex-col space-y-2 flex-1 overflow-y-auto">
        {state.servers.map((server) => (
          <div key={server.id} className="relative group">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "w-12 h-12 rounded-full bg-gray-600 hover:bg-gray-500 hover:rounded-2xl transition-all duration-200 p-0",
                state.currentServer?.id === server.id && "bg-blue-600 rounded-2xl"
              )}
              onClick={() => handleServerClick(server.id)}
            >
              {server.icon_url ? (
                <Avatar className="w-12 h-12">
                  <AvatarImage src={server.icon_url} alt={server.name} />
                  <AvatarFallback className="bg-blue-600 text-white font-semibold">
                    {server.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ) : (
                <div className="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-semibold">
                  {server.name.charAt(0).toUpperCase()}
                </div>
              )}
            </Button>
            
            {/* Server name tooltip */}
            <div className="absolute left-full ml-2 px-2 py-1 bg-black text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
              {server.name}
            </div>

            {/* Active indicator */}
            {state.currentServer?.id === server.id && (
              <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-r-full" />
            )}
          </div>
        ))}
      </div>

      {/* Add Server Button */}
      <div className="relative group">
        <Button
          variant="ghost"
          size="icon"
          className="w-12 h-12 rounded-full bg-gray-600 hover:bg-green-600 hover:rounded-2xl transition-all duration-200"
          onClick={() => setShowCreateDialog(true)}
        >
          <Plus className="w-6 h-6" />
        </Button>
        <div className="absolute left-full ml-2 px-2 py-1 bg-black text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
          Add a Server
        </div>
      </div>

      {/* Join Server Button */}
      <div className="relative group">
        <Button
          variant="ghost"
          size="icon"
          className="w-12 h-12 rounded-full bg-gray-600 hover:bg-purple-600 hover:rounded-2xl transition-all duration-200"
          onClick={() => setShowJoinDialog(true)}
        >
          <Hash className="w-6 h-6" />
        </Button>
        <div className="absolute left-full ml-2 px-2 py-1 bg-black text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
          Join a Server
        </div>
      </div>

      {/* Dialogs */}
      <CreateServerDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog} 
      />
      <JoinServerDialog 
        open={showJoinDialog} 
        onOpenChange={setShowJoinDialog} 
      />
    </div>
  );
}
