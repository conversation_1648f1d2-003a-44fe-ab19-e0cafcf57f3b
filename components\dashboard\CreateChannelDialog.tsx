'use client';

import { useState } from 'react';
import { useApp } from '@/lib/contexts/AppContext';
import { channelOperations } from '@/lib/supabase/database';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { Hash, Volume2 } from 'lucide-react';
import type { ChannelType } from '@/lib/types';

interface CreateChannelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultType?: ChannelType;
}

export function CreateChannelDialog({ open, onOpenChange, defaultType = 'text' }: CreateChannelDialogProps) {
  const { state, actions } = useApp();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<ChannelType>(defaultType);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      setError('Channel name is required');
      return;
    }

    if (!state.currentServer) {
      setError('No server selected');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await channelOperations.createChannel(
        state.currentServer.id,
        name.trim(),
        type,
        description.trim() || undefined
      );
      
      // Reload server data to get the new channel
      await actions.loadUserData();
      
      // Reset form and close dialog
      setName('');
      setDescription('');
      setType('text');
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating channel:', error);
      setError('Failed to create channel. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setName('');
      setDescription('');
      setType(defaultType);
      setError('');
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px] bg-gray-800 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white">Create Channel</DialogTitle>
          <DialogDescription className="text-gray-400">
            Create a new channel in {state.currentServer?.name}.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Channel Type */}
          <div className="space-y-3">
            <Label className="text-white">Channel Type</Label>
            <div className="grid grid-cols-2 gap-2">
              <Button
                type="button"
                variant={type === 'text' ? 'default' : 'outline'}
                className={`justify-start h-auto p-3 ${
                  type === 'text' 
                    ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                    : 'border-gray-600 text-gray-300 hover:bg-gray-700'
                }`}
                onClick={() => setType('text')}
              >
                <div className="flex flex-col items-start">
                  <div className="flex items-center space-x-2 mb-1">
                    <Hash className="w-4 h-4" />
                    <span className="font-medium">Text</span>
                  </div>
                  <span className="text-xs opacity-80">Send messages, images, and files</span>
                </div>
              </Button>
              
              <Button
                type="button"
                variant={type === 'voice' ? 'default' : 'outline'}
                className={`justify-start h-auto p-3 ${
                  type === 'voice' 
                    ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                    : 'border-gray-600 text-gray-300 hover:bg-gray-700'
                }`}
                onClick={() => setType('voice')}
              >
                <div className="flex flex-col items-start">
                  <div className="flex items-center space-x-2 mb-1">
                    <Volume2 className="w-4 h-4" />
                    <span className="font-medium">Voice</span>
                  </div>
                  <span className="text-xs opacity-80">Talk with voice and video</span>
                </div>
              </Button>
            </div>
          </div>

          {/* Channel Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-white">
              Channel Name *
            </Label>
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                {type === 'text' ? <Hash className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </div>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
                placeholder={type === 'text' ? 'general' : 'General'}
                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 pl-10"
                maxLength={100}
                disabled={isLoading}
              />
            </div>
            <p className="text-xs text-gray-500">
              {type === 'text' 
                ? 'Use lowercase letters, numbers, and dashes only'
                : 'Voice channel names can contain spaces and capitals'
              }
            </p>
          </div>
          
          {/* Channel Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-white">
              Description (Optional)
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={`What's this ${type} channel about?`}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 resize-none"
              rows={3}
              maxLength={500}
              disabled={isLoading}
            />
          </div>

          {error && (
            <div className="text-red-400 text-sm">
              {error}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !name.trim()}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Creating...
                </>
              ) : (
                'Create Channel'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
