# Stack - Discord-like Chat Application

A modern, real-time Discord-like chat application built with Next.js 15, Supabase, and TypeScript. Features real-time messaging, server management, voice channels, and a comprehensive user experience.

<p align="center">
  <a href="#features"><strong>Features</strong></a> ·
  <a href="#demo"><strong>Demo</strong></a> ·
  <a href="#deploy-to-vercel"><strong>Deploy to Vercel</strong></a> ·
  <a href="#clone-and-run-locally"><strong>Clone and run locally</strong></a> ·
  <a href="#feedback-and-issues"><strong>Feedback and issues</strong></a>
  <a href="#more-supabase-examples"><strong>More Examples</strong></a>
</p>
<br/>

## Features

### Core Features
- **Real-time Messaging**: Instant message delivery with Supabase Realtime
- **Server Management**: Create, join, and manage Discord-like servers
- **Channel System**: Text and voice channels with categories
- **User Authentication**: Secure authentication with Supa<PERSON> Auth
- **File Uploads**: Support for images and file attachments
- **Message Reactions**: React to messages with emojis
- **Typing Indicators**: See when users are typing
- **User Presence**: Online/offline status tracking
- **Responsive Design**: Works on desktop and mobile

### Technical Features
- **Next.js 15** with App Router
- **Supabase** for backend (Database, Auth, Storage, Realtime)
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **Real-time subscriptions** for live updates
- **Row Level Security (RLS)** for data protection
- **Optimistic UI updates** for better UX

## Demo

You can view a fully working demo at [demo-nextjs-with-supabase.vercel.app](https://demo-nextjs-with-supabase.vercel.app/).

## Deploy to Vercel

Vercel deployment will guide you through creating a Supabase account and project.

After installation of the Supabase integration, all relevant environment variables will be assigned to the project so the deployment is fully functioning.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&project-name=nextjs-with-supabase&repository-name=nextjs-with-supabase&demo-title=nextjs-with-supabase&demo-description=This+starter+configures+Supabase+Auth+to+use+cookies%2C+making+the+user%27s+session+available+throughout+the+entire+Next.js+app+-+Client+Components%2C+Server+Components%2C+Route+Handlers%2C+Server+Actions+and+Middleware.&demo-url=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2F&external-id=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&demo-image=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2Fopengraph-image.png)

The above will also clone the Starter kit to your GitHub, you can clone that locally and develop locally.

If you wish to just develop locally and not deploy to Vercel, [follow the steps below](#clone-and-run-locally).

## Setup Instructions

### Prerequisites
- Node.js 18+
- A Supabase account and project

### 1. Clone and Install

```bash
git clone <repository-url>
cd stack-chat
npm install
```

### 2. Supabase Setup

1. Create a new Supabase project at [database.new](https://database.new)
2. Go to your project's SQL Editor
3. Run the complete `schema.sql` file to set up all tables, functions, and policies
4. Go to Project Settings > API to get your credentials

### 3. Environment Variables

Create a `.env.local` file in the root directory:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Storage Setup (Optional)

For file uploads, create storage buckets in Supabase:
1. Go to Storage in your Supabase dashboard
2. Create buckets named `files` and `avatars`
3. Set appropriate policies for public access

### 5. Run the Application

```bash
npm run dev
```

The application will be available at [localhost:3000](http://localhost:3000)

### 6. First Steps

1. Sign up for an account
2. Create your first server
3. Start chatting in real-time!

> Check out [the docs for Local Development](https://supabase.com/docs/guides/getting-started/local-development) to also run Supabase locally.

## Feedback and issues

Please file feedback and issues over on the [Supabase GitHub org](https://github.com/supabase/supabase/issues/new/choose).

## More Supabase examples

- [Next.js Subscription Payments Starter](https://github.com/vercel/nextjs-subscription-payments)
- [Cookie-based Auth and the Next.js 13 App Router (free course)](https://youtube.com/playlist?list=PL5S4mPUpp4OtMhpnp93EFSo42iQ40XjbF)
- [Supabase Auth and the Next.js App Router](https://github.com/supabase/supabase/tree/master/examples/auth/nextjs)
