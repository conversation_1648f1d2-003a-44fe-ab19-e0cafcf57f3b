# Stack - Discord-like Chat Application

A modern, real-time Discord-like chat application built with Next.js 15, Supabase, and TypeScript. Features real-time messaging, server management, voice channels, and a comprehensive user experience.

<p align="center">
  <a href="#features"><strong>Features</strong></a> ·
  <a href="#demo"><strong>Demo</strong></a> ·
  <a href="#deploy-to-vercel"><strong>Deploy to Vercel</strong></a> ·
  <a href="#clone-and-run-locally"><strong>Clone and run locally</strong></a> ·
  <a href="#feedback-and-issues"><strong>Feedback and issues</strong></a>
  <a href="#more-supabase-examples"><strong>More Examples</strong></a>
</p>
<br/>

## Features

### Core Features
- **Real-time Messaging**: Instant message delivery with Supabase Realtime
- **Server Management**: Create, join, and manage Discord-like servers
- **Channel System**: Text and voice channels with categories
- **User Authentication**: Secure authentication with Supa<PERSON> Auth
- **File Uploads**: Support for images and file attachments
- **Message Reactions**: React to messages with emojis
- **Typing Indicators**: See when users are typing
- **User Presence**: Online/offline status tracking
- **Responsive Design**: Works on desktop and mobile

### Technical Features
- **Next.js 15** with App Router
- **Supabase** for backend (Database, Auth, Storage, Realtime)
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **shadcn/ui** for UI components
- **Real-time subscriptions** for live updates
- **Row Level Security (RLS)** for data protection
- **Optimistic UI updates** for better UX

## Demo

You can view a fully working demo at [demo-nextjs-with-supabase.vercel.app](https://demo-nextjs-with-supabase.vercel.app/).

## Deploy to Vercel

Vercel deployment will guide you through creating a Supabase account and project.

After installation of the Supabase integration, all relevant environment variables will be assigned to the project so the deployment is fully functioning.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&project-name=nextjs-with-supabase&repository-name=nextjs-with-supabase&demo-title=nextjs-with-supabase&demo-description=This+starter+configures+Supabase+Auth+to+use+cookies%2C+making+the+user%27s+session+available+throughout+the+entire+Next.js+app+-+Client+Components%2C+Server+Components%2C+Route+Handlers%2C+Server+Actions+and+Middleware.&demo-url=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2F&external-id=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&demo-image=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2Fopengraph-image.png)

The above will also clone the Starter kit to your GitHub, you can clone that locally and develop locally.

If you wish to just develop locally and not deploy to Vercel, [follow the steps below](#clone-and-run-locally).

## Setup Instructions

### Prerequisites
- Node.js 18+
- A Supabase account and project

### 1. Clone and Install

```bash
git clone <repository-url>
cd stack-chat
npm install
```

### 2. Supabase Setup

1. Create a new Supabase project at [database.new](https://database.new)
2. Go to your project's SQL Editor
3. Run the complete `schema.sql` file to set up all tables, functions, and policies
4. Go to Project Settings > API to get your credentials

### 3. Environment Variables

Create a `.env.local` file in the root directory:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Storage Setup (Optional)

For file uploads, create storage buckets in Supabase:
1. Go to Storage in your Supabase dashboard
2. Create buckets named `files` and `avatars`
3. Set appropriate policies for public access

### 5. Run the Application

```bash
npm run dev
```

The application will be available at [localhost:3000](http://localhost:3000)

### 6. First Steps

1. Sign up for an account
2. Create your first server
3. Start chatting in real-time!

> Check out [the docs for Local Development](https://supabase.com/docs/guides/getting-started/local-development) to also run Supabase locally.

## Architecture Overview

### Frontend
- **Next.js 15** with App Router for modern React development
- **TypeScript** for type safety and better developer experience
- **Tailwind CSS** for utility-first styling
- **shadcn/ui** for beautiful, accessible UI components
- **React Context** for state management
- **Custom hooks** for real-time functionality

### Backend
- **Supabase** as the complete backend solution:
  - **PostgreSQL** database with custom functions and triggers
  - **Row Level Security (RLS)** for data protection
  - **Real-time subscriptions** for live updates
  - **Authentication** with email/password
  - **Storage** for file uploads
  - **Edge Functions** (ready for future features)

### Real-time Features
- **Message delivery** - Instant message broadcasting
- **Typing indicators** - See when users are typing
- **User presence** - Online/offline status tracking
- **Voice channel members** - Real-time voice channel updates
- **Server updates** - Live server and channel changes

### Security
- **Row Level Security** on all tables
- **Server-side validation** for all operations
- **File upload restrictions** and validation
- **Rate limiting** through Supabase
- **XSS protection** via React's built-in sanitization

## Project Structure

```
stack-chat/
├── app/                          # Next.js App Router
│   ├── dashboard/               # Main application dashboard
│   ├── layout.tsx              # Root layout with providers
│   └── page.tsx                # Landing page with auth redirect
├── components/                  # React components
│   ├── dashboard/              # Dashboard-specific components
│   │   ├── DashboardLayout.tsx # Main layout component
│   │   ├── ServerSidebar.tsx   # Server list sidebar
│   │   ├── ChannelSidebar.tsx  # Channel list sidebar
│   │   ├── ChatArea.tsx        # Main chat interface
│   │   ├── MessageList.tsx     # Message display component
│   │   ├── MessageInput.tsx    # Message input component
│   │   ├── UserArea.tsx        # User list sidebar
│   │   └── *.tsx               # Various dialog components
│   └── ui/                     # Reusable UI components
├── lib/                        # Utility libraries
│   ├── contexts/               # React Context providers
│   │   ├── AppContext.tsx      # Global app state
│   │   ├── ChatContext.tsx     # Chat functionality
│   │   └── VoiceContext.tsx    # Voice channel functionality
│   ├── hooks/                  # Custom React hooks
│   │   └── useRealtime.ts      # Real-time subscription hooks
│   ├── supabase/               # Supabase utilities
│   │   ├── client.ts           # Browser client
│   │   ├── server.ts           # Server client
│   │   └── database.ts         # Database operations
│   ├── types.ts                # TypeScript type definitions
│   └── utils.ts                # Utility functions
├── schema.sql                  # Complete database schema
├── setup-database.md          # Database setup guide
└── plan.md                    # Project planning document
```

## Key Features Implemented

### ✅ Core Functionality
- [x] User authentication and profiles
- [x] Server creation and management
- [x] Channel creation (text and voice)
- [x] Real-time messaging
- [x] File uploads and image sharing
- [x] Message reactions
- [x] Typing indicators
- [x] User presence tracking
- [x] Server invitations
- [x] Responsive design

### ✅ Advanced Features
- [x] Optimistic UI updates
- [x] Real-time synchronization
- [x] Row Level Security
- [x] Server and user settings
- [x] Channel management
- [x] Member management
- [x] Toast notifications
- [x] Loading states and error handling

### 🚧 Future Enhancements
- [ ] WebRTC voice communication
- [ ] Video calls
- [ ] Screen sharing
- [ ] Message search
- [ ] Push notifications
- [ ] Mobile app (React Native)
- [ ] Message threads
- [ ] Custom emojis
- [ ] Server roles and permissions
- [ ] Message encryption

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

If you encounter any issues or have questions:

1. Check the [setup guide](setup-database.md)
2. Review the [project plan](plan.md)
3. Open an issue on GitHub
4. Join our community Discord (coming soon!)

---

Built with ❤️ using Next.js, Supabase, and TypeScript.
