'use client';

import { format } from 'date-fns';
import { MessageWithDetails } from '@/lib/types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface MessageListProps {
  messages: MessageWithDetails[];
}

export function MessageList({ messages }: MessageListProps) {
  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <p className="text-lg mb-2">No messages yet</p>
          <p className="text-sm">Be the first to send a message!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      {messages.map((message, index) => {
        const showAvatar = index === 0 || 
          messages[index - 1].user_id !== message.user_id ||
          new Date(message.created_at).getTime() - new Date(messages[index - 1].created_at).getTime() > 300000; // 5 minutes

        return (
          <div key={message.id} className={`flex ${showAvatar ? 'mt-4' : 'mt-1'}`}>
            {showAvatar ? (
              <Avatar className="w-10 h-10 mr-3">
                <AvatarImage src={message.user.avatar_url} alt={message.user.username} />
                <AvatarFallback className="bg-blue-600 text-white">
                  {(message.user.display_name || message.user.username).charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            ) : (
              <div className="w-10 mr-3" />
            )}
            
            <div className="flex-1 min-w-0">
              {showAvatar && (
                <div className="flex items-baseline space-x-2 mb-1">
                  <span className="font-semibold text-white">
                    {message.user.display_name || message.user.username}
                  </span>
                  <span className="text-xs text-gray-500">
                    {format(new Date(message.created_at), 'MMM d, yyyy h:mm a')}
                  </span>
                  {message.edited_at && (
                    <span className="text-xs text-gray-500">(edited)</span>
                  )}
                </div>
              )}
              
              <div className="text-gray-300">
                {message.type === 'text' && message.content && (
                  <p className="break-words">{message.content}</p>
                )}
                
                {message.type === 'image' && message.file_url && (
                  <div className="mt-2">
                    <img 
                      src={message.file_url} 
                      alt={message.file_name || 'Image'}
                      className="max-w-md max-h-96 rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => window.open(message.file_url, '_blank')}
                    />
                    {message.content && (
                      <p className="mt-2 break-words">{message.content}</p>
                    )}
                  </div>
                )}
                
                {message.type === 'file' && message.file_url && (
                  <div className="mt-2">
                    <a 
                      href={message.file_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 rounded-lg p-3 transition-colors"
                    >
                      <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center text-white text-xs font-semibold">
                        📎
                      </div>
                      <div>
                        <div className="text-blue-400 hover:underline">
                          {message.file_name || 'Download file'}
                        </div>
                        {message.file_size && (
                          <div className="text-xs text-gray-500">
                            {(message.file_size / 1024 / 1024).toFixed(2)} MB
                          </div>
                        )}
                      </div>
                    </a>
                    {message.content && (
                      <p className="mt-2 break-words">{message.content}</p>
                    )}
                  </div>
                )}
              </div>
              
              {/* Reactions */}
              {message.reactions && message.reactions.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {message.reactions.reduce((acc, reaction) => {
                    const existing = acc.find(r => r.emoji === reaction.emoji);
                    if (existing) {
                      existing.count++;
                      existing.users.push(reaction.user);
                    } else {
                      acc.push({
                        emoji: reaction.emoji,
                        count: 1,
                        users: [reaction.user]
                      });
                    }
                    return acc;
                  }, [] as Array<{ emoji: string; count: number; users: any[] }>).map((reaction) => (
                    <button
                      key={reaction.emoji}
                      className="inline-flex items-center space-x-1 bg-gray-700 hover:bg-gray-600 rounded-full px-2 py-1 text-xs transition-colors"
                    >
                      <span>{reaction.emoji}</span>
                      <span className="text-gray-300">{reaction.count}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
