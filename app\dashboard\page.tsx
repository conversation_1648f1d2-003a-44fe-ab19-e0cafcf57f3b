'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { AppProvider } from '@/lib/contexts/AppContext';
import { ChatProvider } from '@/lib/contexts/ChatContext';
import { VoiceProvider } from '@/lib/contexts/VoiceContext';
import { ToastProvider } from '@/components/ui/toast';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

export default function DashboardPage() {
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/');
      }
    };

    checkAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_OUT' || !session) {
          router.push('/');
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [router, supabase]);

  return (
    <ToastProvider>
      <AppProvider>
        <ChatProvider>
          <VoiceProvider>
            <DashboardLayout />
          </VoiceProvider>
        </ChatProvider>
      </AppProvider>
    </ToastProvider>
  );
}
