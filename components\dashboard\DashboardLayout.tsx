'use client';

import { useApp } from '@/lib/contexts/AppContext';
import { ServerSidebar } from './ServerSidebar';
import { ChannelSidebar } from './ChannelSidebar';
import { ChatArea } from './ChatArea';
import { UserArea } from './UserArea';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { WelcomeScreen } from './WelcomeScreen';

export function DashboardLayout() {
  const { state } = useApp();

  if (state.isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Error</h1>
          <p className="text-gray-400">{state.error}</p>
        </div>
      </div>
    );
  }

  if (!state.user) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Not Authenticated</h1>
          <p className="text-gray-400">Please sign in to continue.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-gray-900 text-white">
      {/* Server List Sidebar */}
      <div className="w-[72px] bg-gray-800 flex flex-col">
        <ServerSidebar />
      </div>

      {/* Channel Sidebar */}
      {state.currentServer ? (
        <div className="w-60 bg-gray-700 flex flex-col">
          <ChannelSidebar />
        </div>
      ) : null}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {state.currentChannel ? (
          <>
            {/* Chat Area */}
            <div className="flex-1 flex">
              <ChatArea />
              {/* User List (for future implementation) */}
              <div className="w-60 bg-gray-800 hidden lg:block">
                <UserArea />
              </div>
            </div>
          </>
        ) : (
          <WelcomeScreen />
        )}
      </div>
    </div>
  );
}
