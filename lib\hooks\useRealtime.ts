// Real-time hooks for Stack app
'use client';

import { useEffect, useRef, useCallback } from 'react';
import { createClient } from '../supabase/client';
import type { 
  RealtimeChannel, 
  Message, 
  MessageReaction, 
  TypingIndicator,
  ChannelMember,
  User,
  RealtimeEvent
} from '../types';

const supabase = createClient();

// Hook for real-time messages
export function useRealtimeMessages(
  channelId: string | null,
  onNewMessage?: (message: Message) => void,
  onMessageUpdate?: (message: Message) => void,
  onMessageDelete?: (messageId: string) => void
) {
  const channelRef = useRef<any>(null);

  useEffect(() => {
    if (!channelId) return;

    // Subscribe to messages table changes for this channel
    channelRef.current = supabase
      .channel(`messages:${channelId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<Message>) => {
          onNewMessage?.(payload.new);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<Message>) => {
          onMessageUpdate?.(payload.new);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'messages',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<Message>) => {
          onMessageDelete?.(payload.old.id);
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [channelId, onNewMessage, onMessageUpdate, onMessageDelete]);

  return channelRef.current;
}

// Hook for real-time message reactions
export function useRealtimeReactions(
  messageIds: string[],
  onReactionAdd?: (reaction: MessageReaction) => void,
  onReactionRemove?: (reactionId: string) => void
) {
  const channelRef = useRef<any>(null);

  useEffect(() => {
    if (messageIds.length === 0) return;

    const messageFilter = messageIds.map(id => `message_id=eq.${id}`).join(',');

    channelRef.current = supabase
      .channel('message_reactions')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'message_reactions',
          filter: `or=(${messageFilter})`
        },
        (payload: RealtimeEvent<MessageReaction>) => {
          onReactionAdd?.(payload.new);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'message_reactions',
          filter: `or=(${messageFilter})`
        },
        (payload: RealtimeEvent<MessageReaction>) => {
          onReactionRemove?.(payload.old.id);
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [messageIds, onReactionAdd, onReactionRemove]);

  return channelRef.current;
}

// Hook for typing indicators
export function useRealtimeTyping(
  channelId: string | null,
  onTypingStart?: (indicator: TypingIndicator) => void,
  onTypingStop?: (userId: string) => void
) {
  const channelRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const startTyping = useCallback(async () => {
    if (!channelId) return;

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Insert or update typing indicator
    await supabase
      .from('typing_indicators')
      .upsert({
        channel_id: channelId,
        user_id: user.id,
        started_at: new Date().toISOString()
      });

    // Auto-stop typing after 3 seconds
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, [channelId]);

  const stopTyping = useCallback(async () => {
    if (!channelId) return;

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    await supabase
      .from('typing_indicators')
      .delete()
      .eq('channel_id', channelId)
      .eq('user_id', user.id);
  }, [channelId]);

  useEffect(() => {
    if (!channelId) return;

    channelRef.current = supabase
      .channel(`typing:${channelId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'typing_indicators',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<TypingIndicator>) => {
          onTypingStart?.(payload.new);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'typing_indicators',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<TypingIndicator>) => {
          onTypingStop?.(payload.old.user_id);
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [channelId, onTypingStart, onTypingStop]);

  return { startTyping, stopTyping };
}

// Hook for user presence
export function useRealtimePresence(
  serverIds: string[],
  onUserStatusChange?: (userId: string, status: User['status']) => void
) {
  const channelRef = useRef<any>(null);

  useEffect(() => {
    if (serverIds.length === 0) return;

    channelRef.current = supabase
      .channel('user_presence')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users'
        },
        (payload: RealtimeEvent<User>) => {
          if (payload.new.status !== payload.old?.status) {
            onUserStatusChange?.(payload.new.id, payload.new.status);
          }
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [serverIds, onUserStatusChange]);

  return channelRef.current;
}

// Hook for voice channel members
export function useRealtimeVoiceMembers(
  channelId: string | null,
  onMemberJoin?: (member: ChannelMember) => void,
  onMemberLeave?: (userId: string) => void,
  onMemberUpdate?: (member: ChannelMember) => void
) {
  const channelRef = useRef<any>(null);

  useEffect(() => {
    if (!channelId) return;

    channelRef.current = supabase
      .channel(`voice:${channelId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'channel_members',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<ChannelMember>) => {
          onMemberJoin?.(payload.new);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'channel_members',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<ChannelMember>) => {
          onMemberLeave?.(payload.old.user_id);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'channel_members',
          filter: `channel_id=eq.${channelId}`
        },
        (payload: RealtimeEvent<ChannelMember>) => {
          onMemberUpdate?.(payload.new);
        }
      )
      .subscribe();

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [channelId, onMemberJoin, onMemberLeave, onMemberUpdate]);

  return channelRef.current;
}

// Hook for general real-time connection status
export function useRealtimeStatus() {
  const channelRef = useRef<any>(null);

  useEffect(() => {
    channelRef.current = supabase
      .channel('connection_status')
      .subscribe((status) => {
        console.log('Realtime connection status:', status);
      });

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, []);

  return channelRef.current;
}
