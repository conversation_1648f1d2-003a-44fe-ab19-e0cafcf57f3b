'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { channelOperations, fileOperations } from '../supabase/database';
import { useRealtimeMessages, useRealtimeReactions, useRealtimeTyping } from '../hooks/useRealtime';
import { useApp } from './AppContext';
import type { 
  MessageWithDetails, 
  User, 
  ChatContextType,
  MessageReaction
} from '../types';

// Action types
type ChatAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_MESSAGES'; payload: MessageWithDetails[] }
  | { type: 'ADD_MESSAGE'; payload: MessageWithDetails }
  | { type: 'UPDATE_MESSAGE'; payload: MessageWithDetails }
  | { type: 'DELETE_MESSAGE'; payload: string }
  | { type: 'ADD_REACTION'; payload: { messageId: string; reaction: MessageReaction } }
  | { type: 'REMOVE_REACTION'; payload: { messageId: string; reactionId: string } }
  | { type: 'SET_TYPING_USERS'; payload: User[] }
  | { type: 'ADD_TYPING_USER'; payload: User }
  | { type: 'REMOVE_TYPING_USER'; payload: string }
  | { type: 'SET_HAS_MORE'; payload: boolean }
  | { type: 'PREPEND_MESSAGES'; payload: MessageWithDetails[] };

// Initial state
interface ChatState {
  messages: MessageWithDetails[];
  typingUsers: User[];
  isLoadingMessages: boolean;
  hasMoreMessages: boolean;
}

const initialState: ChatState = {
  messages: [],
  typingUsers: [],
  isLoadingMessages: false,
  hasMoreMessages: true
};

// Reducer
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoadingMessages: action.payload };
    
    case 'SET_MESSAGES':
      return { 
        ...state, 
        messages: action.payload.sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        )
      };
    
    case 'ADD_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload].sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        )
      };
    
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(msg => 
          msg.id === action.payload.id ? action.payload : msg
        )
      };
    
    case 'DELETE_MESSAGE':
      return {
        ...state,
        messages: state.messages.filter(msg => msg.id !== action.payload)
      };
    
    case 'ADD_REACTION':
      return {
        ...state,
        messages: state.messages.map(msg => 
          msg.id === action.payload.messageId
            ? { 
                ...msg, 
                reactions: [...(msg.reactions || []), action.payload.reaction]
              }
            : msg
        )
      };
    
    case 'REMOVE_REACTION':
      return {
        ...state,
        messages: state.messages.map(msg => 
          msg.id === action.payload.messageId
            ? { 
                ...msg, 
                reactions: (msg.reactions || []).filter(r => r.id !== action.payload.reactionId)
              }
            : msg
        )
      };
    
    case 'SET_TYPING_USERS':
      return { ...state, typingUsers: action.payload };
    
    case 'ADD_TYPING_USER':
      return {
        ...state,
        typingUsers: state.typingUsers.find(u => u.id === action.payload.id)
          ? state.typingUsers
          : [...state.typingUsers, action.payload]
      };
    
    case 'REMOVE_TYPING_USER':
      return {
        ...state,
        typingUsers: state.typingUsers.filter(u => u.id !== action.payload)
      };
    
    case 'SET_HAS_MORE':
      return { ...state, hasMoreMessages: action.payload };
    
    case 'PREPEND_MESSAGES':
      const existingIds = new Set(state.messages.map(m => m.id));
      const newMessages = action.payload.filter(m => !existingIds.has(m.id));
      return {
        ...state,
        messages: [...newMessages, ...state.messages].sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        )
      };
    
    default:
      return state;
  }
}

// Context
const ChatContext = createContext<ChatContextType | null>(null);

// Provider component
interface ChatProviderProps {
  children: ReactNode;
}

export function ChatProvider({ children }: ChatProviderProps) {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const { state: appState } = useApp();
  const currentChannelId = appState.currentChannel?.id || null;

  // Real-time subscriptions
  useRealtimeMessages(
    currentChannelId,
    (message) => {
      // Get user details and add to message
      const messageWithDetails: MessageWithDetails = {
        ...message,
        user: appState.user || {
          id: message.user_id,
          username: 'Unknown',
          display_name: 'Unknown User',
          status: 'offline',
          last_seen: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        reactions: []
      };
      dispatch({ type: 'ADD_MESSAGE', payload: messageWithDetails });
    },
    (message) => {
      const messageWithDetails: MessageWithDetails = {
        ...message,
        user: appState.user || {
          id: message.user_id,
          username: 'Unknown',
          display_name: 'Unknown User',
          status: 'offline',
          last_seen: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        reactions: []
      };
      dispatch({ type: 'UPDATE_MESSAGE', payload: messageWithDetails });
    },
    (messageId) => {
      dispatch({ type: 'DELETE_MESSAGE', payload: messageId });
    }
  );

  useRealtimeReactions(
    state.messages.map(m => m.id),
    (reaction) => {
      dispatch({ type: 'ADD_REACTION', payload: { messageId: reaction.message_id, reaction } });
    },
    (reactionId) => {
      // Find which message this reaction belongs to
      const message = state.messages.find(m => 
        m.reactions?.some(r => r.id === reactionId)
      );
      if (message) {
        dispatch({ type: 'REMOVE_REACTION', payload: { messageId: message.id, reactionId } });
      }
    }
  );

  const { startTyping, stopTyping } = useRealtimeTyping(
    currentChannelId,
    (indicator) => {
      // Add typing user (you'd need to fetch user details)
      if (indicator.user_id !== appState.user?.id) {
        const typingUser: User = {
          id: indicator.user_id,
          username: 'User',
          display_name: 'Typing User',
          status: 'online',
          last_seen: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        dispatch({ type: 'ADD_TYPING_USER', payload: typingUser });
      }
    },
    (userId) => {
      dispatch({ type: 'REMOVE_TYPING_USER', payload: userId });
    }
  );

  // Load messages when channel changes
  useEffect(() => {
    if (!currentChannelId) {
      dispatch({ type: 'SET_MESSAGES', payload: [] });
      return;
    }

    const loadMessages = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        const messages = await channelOperations.getChannelMessages(currentChannelId, 50);
        dispatch({ type: 'SET_MESSAGES', payload: messages });
        dispatch({ type: 'SET_HAS_MORE', payload: messages.length === 50 });
      } catch (error) {
        console.error('Error loading messages:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    loadMessages();
  }, [currentChannelId]);

  // Actions
  const sendMessage = async (content: string, file?: File, replyTo?: string) => {
    if (!currentChannelId) return;
    if (!content.trim() && !file) return;

    try {
      let fileUrl: string | undefined;
      let fileName: string | undefined;
      let fileSize: number | undefined;

      if (file) {
        fileUrl = await fileOperations.uploadFile(file);
        fileName = file.name;
        fileSize = file.size;
      }

      await channelOperations.sendMessage(
        currentChannelId,
        content.trim() || undefined,
        fileUrl,
        fileName,
        fileSize,
        replyTo
      );

      // Stop typing after sending
      stopTyping();
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  };

  const loadMoreMessages = async () => {
    if (!currentChannelId || !state.hasMoreMessages || state.isLoadingMessages) return;

    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const oldestMessage = state.messages[0];
      const messages = await channelOperations.getChannelMessages(
        currentChannelId,
        50,
        oldestMessage?.id
      );
      
      dispatch({ type: 'PREPEND_MESSAGES', payload: messages });
      dispatch({ type: 'SET_HAS_MORE', payload: messages.length === 50 });
    } catch (error) {
      console.error('Error loading more messages:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const editMessage = async (messageId: string, content: string) => {
    try {
      await channelOperations.editMessage(messageId, content);
    } catch (error) {
      console.error('Error editing message:', error);
      throw error;
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      await channelOperations.deleteMessage(messageId);
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  };

  const addReaction = async (messageId: string, emoji: string) => {
    try {
      await channelOperations.addReaction(messageId, emoji);
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
  };

  const removeReaction = async (messageId: string, emoji: string) => {
    try {
      await channelOperations.removeReaction(messageId, emoji);
    } catch (error) {
      console.error('Error removing reaction:', error);
      throw error;
    }
  };

  const contextValue: ChatContextType = {
    messages: state.messages,
    typingUsers: state.typingUsers,
    isLoadingMessages: state.isLoadingMessages,
    hasMoreMessages: state.hasMoreMessages,
    sendMessage,
    loadMoreMessages,
    editMessage,
    deleteMessage,
    addReaction,
    removeReaction
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
}

// Hook to use the context
export function useChat() {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}
