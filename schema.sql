-- Stack Discord-like App Database Schema
-- Complete schema with tables, RLS policies, functions, and triggers

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Custom types
CREATE TYPE user_status AS ENUM ('online', 'away', 'busy', 'offline');
CREATE TYPE channel_type AS ENUM ('text', 'voice');
CREATE TYPE message_type AS ENUM ('text', 'image', 'file', 'system');
CREATE TYPE server_role AS ENUM ('owner', 'admin', 'moderator', 'member');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    username VARCHAR(32) UNIQUE NOT NULL,
    display_name VA<PERSON>HA<PERSON>(64),
    avatar_url TEXT,
    bio TEXT,
    status user_status DEFAULT 'offline',
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Servers table
CREATE TABLE public.servers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_url TEXT,
    owner_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    invite_code VARCHAR(10) UNIQUE DEFAULT encode(gen_random_bytes(5), 'hex'),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Server members table
CREATE TABLE public.server_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    server_id UUID NOT NULL REFERENCES public.servers(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    role server_role DEFAULT 'member',
    nickname VARCHAR(64),
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(server_id, user_id)
);

-- Channels table
CREATE TABLE public.channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    server_id UUID NOT NULL REFERENCES public.servers(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type channel_type DEFAULT 'text',
    position INTEGER DEFAULT 0,
    parent_id UUID REFERENCES public.channels(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Messages table
CREATE TABLE public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID NOT NULL REFERENCES public.channels(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    content TEXT,
    type message_type DEFAULT 'text',
    file_url TEXT,
    file_name TEXT,
    file_size BIGINT,
    reply_to UUID REFERENCES public.messages(id) ON DELETE SET NULL,
    edited_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Message reactions table
CREATE TABLE public.message_reactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    emoji VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(message_id, user_id, emoji)
);

-- Channel members table (for voice channels)
CREATE TABLE public.channel_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID NOT NULL REFERENCES public.channels(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    is_muted BOOLEAN DEFAULT FALSE,
    is_deafened BOOLEAN DEFAULT FALSE,
    UNIQUE(channel_id, user_id)
);

-- Server invites table
CREATE TABLE public.server_invites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    server_id UUID NOT NULL REFERENCES public.servers(id) ON DELETE CASCADE,
    inviter_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    code VARCHAR(10) UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(5), 'hex'),
    max_uses INTEGER,
    uses INTEGER DEFAULT 0,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Typing indicators table
CREATE TABLE public.typing_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID NOT NULL REFERENCES public.channels(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(channel_id, user_id)
);

-- User sessions table (for presence)
CREATE TABLE public.user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    status user_status DEFAULT 'online',
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, session_id)
);

-- Indexes for performance
CREATE INDEX idx_servers_owner_id ON public.servers(owner_id);
CREATE INDEX idx_server_members_server_id ON public.server_members(server_id);
CREATE INDEX idx_server_members_user_id ON public.server_members(user_id);
CREATE INDEX idx_channels_server_id ON public.channels(server_id);
CREATE INDEX idx_messages_channel_id ON public.messages(channel_id);
CREATE INDEX idx_messages_user_id ON public.messages(user_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX idx_message_reactions_message_id ON public.message_reactions(message_id);
CREATE INDEX idx_channel_members_channel_id ON public.channel_members(channel_id);
CREATE INDEX idx_typing_indicators_channel_id ON public.typing_indicators(channel_id);
CREATE INDEX idx_user_sessions_user_id ON public.user_sessions(user_id);

-- Updated at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_servers_updated_at BEFORE UPDATE ON public.servers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON public.channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.servers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.server_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.channel_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.server_invites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.typing_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users policies
CREATE POLICY "Users can view all users" ON public.users
    FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Servers policies
CREATE POLICY "Users can view servers they are members of" ON public.servers
    FOR SELECT USING (
        id IN (
            SELECT server_id FROM public.server_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create servers" ON public.servers
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Server owners can update their servers" ON public.servers
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Server owners can delete their servers" ON public.servers
    FOR DELETE USING (auth.uid() = owner_id);

-- Server members policies
CREATE POLICY "Users can view server members of servers they belong to" ON public.server_members
    FOR SELECT USING (
        server_id IN (
            SELECT server_id FROM public.server_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can join servers" ON public.server_members
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave servers" ON public.server_members
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Server owners and admins can manage members" ON public.server_members
    FOR UPDATE USING (
        server_id IN (
            SELECT server_id FROM public.server_members
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- Channels policies
CREATE POLICY "Users can view channels of servers they belong to" ON public.channels
    FOR SELECT USING (
        server_id IN (
            SELECT server_id FROM public.server_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Server owners and admins can manage channels" ON public.channels
    FOR ALL USING (
        server_id IN (
            SELECT server_id FROM public.server_members
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin', 'moderator')
        )
    );

-- Messages policies
CREATE POLICY "Users can view messages in channels they have access to" ON public.messages
    FOR SELECT USING (
        channel_id IN (
            SELECT c.id FROM public.channels c
            JOIN public.server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can send messages to channels they have access to" ON public.messages
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        channel_id IN (
            SELECT c.id FROM public.channels c
            JOIN public.server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own messages" ON public.messages
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own messages" ON public.messages
    FOR DELETE USING (auth.uid() = user_id);

-- Message reactions policies
CREATE POLICY "Users can view reactions on accessible messages" ON public.message_reactions
    FOR SELECT USING (
        message_id IN (
            SELECT m.id FROM public.messages m
            JOIN public.channels c ON m.channel_id = c.id
            JOIN public.server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can add reactions to accessible messages" ON public.message_reactions
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        message_id IN (
            SELECT m.id FROM public.messages m
            JOIN public.channels c ON m.channel_id = c.id
            JOIN public.server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can remove their own reactions" ON public.message_reactions
    FOR DELETE USING (auth.uid() = user_id);

-- Channel members policies (for voice channels)
CREATE POLICY "Users can view voice channel members" ON public.channel_members
    FOR SELECT USING (
        channel_id IN (
            SELECT c.id FROM public.channels c
            JOIN public.server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can join voice channels" ON public.channel_members
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        channel_id IN (
            SELECT c.id FROM public.channels c
            JOIN public.server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can leave voice channels" ON public.channel_members
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can update their voice settings" ON public.channel_members
    FOR UPDATE USING (auth.uid() = user_id);

-- Server invites policies
CREATE POLICY "Users can view invites for servers they belong to" ON public.server_invites
    FOR SELECT USING (
        server_id IN (
            SELECT server_id FROM public.server_members
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Server members can create invites" ON public.server_invites
    FOR INSERT WITH CHECK (
        auth.uid() = inviter_id AND
        server_id IN (
            SELECT server_id FROM public.server_members
            WHERE user_id = auth.uid()
        )
    );

-- Typing indicators policies
CREATE POLICY "Users can view typing indicators in accessible channels" ON public.typing_indicators
    FOR SELECT USING (
        channel_id IN (
            SELECT c.id FROM public.channels c
            JOIN public.server_members sm ON c.server_id = sm.server_id
            WHERE sm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage their own typing indicators" ON public.typing_indicators
    FOR ALL USING (auth.uid() = user_id);

-- User sessions policies
CREATE POLICY "Users can view their own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own sessions" ON public.user_sessions
    FOR ALL USING (auth.uid() = user_id);

-- Database Functions

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, username, display_name)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'display_name', split_part(NEW.email, '@', 1))
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update user last seen
CREATE OR REPLACE FUNCTION public.update_user_last_seen(user_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE public.users
    SET last_seen = NOW(), status = 'online'
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old typing indicators
CREATE OR REPLACE FUNCTION public.cleanup_typing_indicators()
RETURNS VOID AS $$
BEGIN
    DELETE FROM public.typing_indicators
    WHERE started_at < NOW() - INTERVAL '10 seconds';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get server members with user details
CREATE OR REPLACE FUNCTION public.get_server_members(server_uuid UUID)
RETURNS TABLE (
    id UUID,
    username VARCHAR,
    display_name VARCHAR,
    avatar_url TEXT,
    status user_status,
    role server_role,
    nickname VARCHAR,
    joined_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id,
        u.username,
        u.display_name,
        u.avatar_url,
        u.status,
        sm.role,
        sm.nickname,
        sm.joined_at
    FROM public.users u
    JOIN public.server_members sm ON u.id = sm.user_id
    WHERE sm.server_id = server_uuid
    ORDER BY sm.role, u.username;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's servers
CREATE OR REPLACE FUNCTION public.get_user_servers(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    description TEXT,
    icon_url TEXT,
    owner_id UUID,
    role server_role,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.name,
        s.description,
        s.icon_url,
        s.owner_id,
        sm.role,
        s.created_at
    FROM public.servers s
    JOIN public.server_members sm ON s.id = sm.server_id
    WHERE sm.user_id = user_uuid
    ORDER BY s.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get channel messages with user details
CREATE OR REPLACE FUNCTION public.get_channel_messages(
    channel_uuid UUID,
    message_limit INTEGER DEFAULT 50,
    before_message UUID DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    type message_type,
    file_url TEXT,
    file_name TEXT,
    file_size BIGINT,
    reply_to UUID,
    edited_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    user_id UUID,
    username VARCHAR,
    display_name VARCHAR,
    avatar_url TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.content,
        m.type,
        m.file_url,
        m.file_name,
        m.file_size,
        m.reply_to,
        m.edited_at,
        m.created_at,
        u.id as user_id,
        u.username,
        u.display_name,
        u.avatar_url
    FROM public.messages m
    JOIN public.users u ON m.user_id = u.id
    WHERE m.channel_id = channel_uuid
    AND (before_message IS NULL OR m.created_at < (
        SELECT created_at FROM public.messages WHERE id = before_message
    ))
    ORDER BY m.created_at DESC
    LIMIT message_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to join server by invite code
CREATE OR REPLACE FUNCTION public.join_server_by_invite(invite_code_param VARCHAR)
RETURNS TABLE (
    success BOOLEAN,
    server_id UUID,
    message TEXT
) AS $$
DECLARE
    invite_record RECORD;
    user_uuid UUID;
BEGIN
    user_uuid := auth.uid();

    -- Check if user is authenticated
    IF user_uuid IS NULL THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'User not authenticated';
        RETURN;
    END IF;

    -- Get invite details
    SELECT * INTO invite_record
    FROM public.server_invites si
    WHERE si.code = invite_code_param
    AND (si.expires_at IS NULL OR si.expires_at > NOW())
    AND (si.max_uses IS NULL OR si.uses < si.max_uses);

    -- Check if invite exists and is valid
    IF invite_record IS NULL THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Invalid or expired invite';
        RETURN;
    END IF;

    -- Check if user is already a member
    IF EXISTS (
        SELECT 1 FROM public.server_members
        WHERE server_id = invite_record.server_id AND user_id = user_uuid
    ) THEN
        RETURN QUERY SELECT FALSE, invite_record.server_id, 'Already a member of this server';
        RETURN;
    END IF;

    -- Add user to server
    INSERT INTO public.server_members (server_id, user_id, role)
    VALUES (invite_record.server_id, user_uuid, 'member');

    -- Update invite usage
    UPDATE public.server_invites
    SET uses = uses + 1
    WHERE id = invite_record.id;

    RETURN QUERY SELECT TRUE, invite_record.server_id, 'Successfully joined server';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create default channels when server is created
CREATE OR REPLACE FUNCTION public.create_default_channels()
RETURNS TRIGGER AS $$
BEGIN
    -- Create general text channel
    INSERT INTO public.channels (server_id, name, type, position)
    VALUES (NEW.id, 'general', 'text', 0);

    -- Create general voice channel
    INSERT INTO public.channels (server_id, name, type, position)
    VALUES (NEW.id, 'General', 'voice', 1);

    -- Add server owner as member
    INSERT INTO public.server_members (server_id, user_id, role)
    VALUES (NEW.id, NEW.owner_id, 'owner');

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create default channels
CREATE TRIGGER create_default_channels_trigger
    AFTER INSERT ON public.servers
    FOR EACH ROW EXECUTE FUNCTION public.create_default_channels();

-- Function to broadcast message events
CREATE OR REPLACE FUNCTION public.broadcast_message()
RETURNS TRIGGER AS $$
BEGIN
    -- Broadcast new message to channel
    PERFORM pg_notify(
        'message_' || NEW.channel_id::text,
        json_build_object(
            'type', 'new_message',
            'message', row_to_json(NEW)
        )::text
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for message broadcasting
CREATE TRIGGER broadcast_message_trigger
    AFTER INSERT ON public.messages
    FOR EACH ROW EXECUTE FUNCTION public.broadcast_message();

-- Function to handle user presence updates
CREATE OR REPLACE FUNCTION public.update_user_presence(
    user_uuid UUID,
    new_status user_status DEFAULT 'online'
)
RETURNS VOID AS $$
BEGIN
    -- Update user status
    UPDATE public.users
    SET status = new_status, last_seen = NOW()
    WHERE id = user_uuid;

    -- Broadcast presence update
    PERFORM pg_notify(
        'user_presence',
        json_build_object(
            'user_id', user_uuid,
            'status', new_status,
            'timestamp', NOW()
        )::text
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired sessions and update user status
CREATE OR REPLACE FUNCTION public.cleanup_user_sessions()
RETURNS VOID AS $$
BEGIN
    -- Mark users as offline if no active sessions
    UPDATE public.users
    SET status = 'offline'
    WHERE id IN (
        SELECT DISTINCT user_id
        FROM public.user_sessions
        WHERE last_activity < NOW() - INTERVAL '5 minutes'
    );

    -- Remove expired sessions
    DELETE FROM public.user_sessions
    WHERE last_activity < NOW() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable Realtime for tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
ALTER PUBLICATION supabase_realtime ADD TABLE public.message_reactions;
ALTER PUBLICATION supabase_realtime ADD TABLE public.typing_indicators;
ALTER PUBLICATION supabase_realtime ADD TABLE public.channel_members;
ALTER PUBLICATION supabase_realtime ADD TABLE public.users;
ALTER PUBLICATION supabase_realtime ADD TABLE public.server_members;

-- Create indexes for realtime performance
CREATE INDEX idx_messages_realtime ON public.messages(channel_id, created_at DESC);
CREATE INDEX idx_typing_realtime ON public.typing_indicators(channel_id, started_at DESC);
CREATE INDEX idx_channel_members_realtime ON public.channel_members(channel_id, joined_at DESC);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Insert some sample data for development
INSERT INTO public.users (id, username, display_name, status) VALUES
    ('00000000-0000-0000-0000-000000000001', 'admin', 'Administrator', 'online')
ON CONFLICT (id) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE public.users IS 'Extended user profiles with presence information';
COMMENT ON TABLE public.servers IS 'Discord-like servers/guilds';
COMMENT ON TABLE public.channels IS 'Text and voice channels within servers';
COMMENT ON TABLE public.messages IS 'Chat messages with support for files and replies';
COMMENT ON TABLE public.server_members IS 'User membership in servers with roles';
COMMENT ON TABLE public.channel_members IS 'Users currently in voice channels';
COMMENT ON TABLE public.typing_indicators IS 'Real-time typing indicators';
COMMENT ON TABLE public.user_sessions IS 'User presence and session management';

-- Schema version for migrations
CREATE TABLE IF NOT EXISTS public.schema_version (
    version INTEGER PRIMARY KEY,
    applied_at TIMESTAMPTZ DEFAULT NOW()
);

INSERT INTO public.schema_version (version) VALUES (1) ON CONFLICT DO NOTHING;
