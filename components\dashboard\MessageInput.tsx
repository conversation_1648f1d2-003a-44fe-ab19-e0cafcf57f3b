'use client';

import { useState, useRef } from 'react';
import { Send, Paperclip, Smile } from 'lucide-react';
import { useApp } from '@/lib/contexts/AppContext';
import { useChat } from '@/lib/contexts/ChatContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

export function MessageInput() {
  const { state } = useApp();
  const { sendMessage } = useChat();
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() || isLoading) return;

    setIsLoading(true);
    try {
      await sendMessage(content);
      setContent('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    setIsLoading(true);
    try {
      await sendMessage(content || '', file);
      setContent('');
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Failed to upload file');
    } finally {
      setIsLoading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  if (!state.currentChannel) return null;

  return (
    <form onSubmit={handleSubmit} className="relative">
      <div className="flex items-end space-x-2 bg-gray-700 rounded-lg p-3">
        {/* File Upload */}
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileUpload}
          className="hidden"
          accept="image/*,.pdf,.doc,.docx,.txt,.zip,.rar"
        />
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="text-gray-400 hover:text-white"
          onClick={() => fileInputRef.current?.click()}
          disabled={isLoading}
        >
          <Paperclip className="w-5 h-5" />
        </Button>

        {/* Message Input */}
        <div className="flex-1">
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={`Message #${state.currentChannel.name}`}
            className="min-h-[40px] max-h-32 bg-transparent border-none resize-none text-white placeholder-gray-400 focus:ring-0 focus:outline-none"
            disabled={isLoading}
            rows={1}
          />
        </div>

        {/* Emoji Button (placeholder) */}
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="text-gray-400 hover:text-white"
          disabled={isLoading}
        >
          <Smile className="w-5 h-5" />
        </Button>

        {/* Send Button */}
        <Button
          type="submit"
          size="icon"
          className="bg-blue-600 hover:bg-blue-700 text-white"
          disabled={!content.trim() || isLoading}
        >
          <Send className="w-5 h-5" />
        </Button>
      </div>
      
      {/* Character count */}
      {content.length > 1800 && (
        <div className="absolute -top-6 right-0 text-xs text-gray-500">
          {content.length}/2000
        </div>
      )}
    </form>
  );
}
