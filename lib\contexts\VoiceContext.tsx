'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { channelOperations } from '../supabase/database';
import { useRealtimeVoiceMembers } from '../hooks/useRealtime';
import { useApp } from './AppContext';
import type { 
  Channel, 
  ChannelMember, 
  VoiceContextType 
} from '../types';

// Action types
type VoiceAction = 
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_MUTED'; payload: boolean }
  | { type: 'SET_DEAFENED'; payload: boolean }
  | { type: 'SET_CURRENT_CHANNEL'; payload: Channel | null }
  | { type: 'SET_CHANNEL_MEMBERS'; payload: ChannelMember[] }
  | { type: 'ADD_MEMBER'; payload: ChannelMember }
  | { type: 'REMOVE_MEMBER'; payload: string }
  | { type: 'UPDATE_MEMBER'; payload: ChannelMember };

// Initial state
interface VoiceState {
  isConnected: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  currentChannel: Channel | null;
  channelMembers: ChannelMember[];
}

const initialState: VoiceState = {
  isConnected: false,
  isMuted: false,
  isDeafened: false,
  currentChannel: null,
  channelMembers: []
};

// Reducer
function voiceReducer(state: VoiceState, action: VoiceAction): VoiceState {
  switch (action.type) {
    case 'SET_CONNECTED':
      return { ...state, isConnected: action.payload };
    
    case 'SET_MUTED':
      return { ...state, isMuted: action.payload };
    
    case 'SET_DEAFENED':
      return { ...state, isDeafened: action.payload };
    
    case 'SET_CURRENT_CHANNEL':
      return { 
        ...state, 
        currentChannel: action.payload,
        channelMembers: action.payload ? state.channelMembers : []
      };
    
    case 'SET_CHANNEL_MEMBERS':
      return { ...state, channelMembers: action.payload };
    
    case 'ADD_MEMBER':
      return {
        ...state,
        channelMembers: [...state.channelMembers, action.payload]
      };
    
    case 'REMOVE_MEMBER':
      return {
        ...state,
        channelMembers: state.channelMembers.filter(m => m.user_id !== action.payload)
      };
    
    case 'UPDATE_MEMBER':
      return {
        ...state,
        channelMembers: state.channelMembers.map(m => 
          m.user_id === action.payload.user_id ? action.payload : m
        )
      };
    
    default:
      return state;
  }
}

// Context
const VoiceContext = createContext<VoiceContextType | null>(null);

// Provider component
interface VoiceProviderProps {
  children: ReactNode;
}

export function VoiceProvider({ children }: VoiceProviderProps) {
  const [state, dispatch] = useReducer(voiceReducer, initialState);
  const { state: appState } = useApp();

  // Real-time voice channel subscriptions
  useRealtimeVoiceMembers(
    state.currentChannel?.id || null,
    (member) => {
      dispatch({ type: 'ADD_MEMBER', payload: member });
    },
    (userId) => {
      dispatch({ type: 'REMOVE_MEMBER', payload: userId });
    },
    (member) => {
      dispatch({ type: 'UPDATE_MEMBER', payload: member });
    }
  );

  // Connect to voice channel
  const connect = async (channelId: string) => {
    try {
      if (!appState.currentServer) return;

      const channel = appState.currentServer.channels.find(c => c.id === channelId);
      if (!channel || channel.type !== 'voice') return;

      // TODO: Implement WebRTC connection logic here
      // For now, just simulate joining the channel
      
      // Add user to channel_members table
      // This would be handled by the backend in a real implementation
      
      dispatch({ type: 'SET_CURRENT_CHANNEL', payload: channel });
      dispatch({ type: 'SET_CONNECTED', payload: true });
      
      console.log(`Connected to voice channel: ${channel.name}`);
    } catch (error) {
      console.error('Error connecting to voice channel:', error);
    }
  };

  // Disconnect from voice channel
  const disconnect = async () => {
    try {
      if (!state.currentChannel) return;

      // TODO: Implement WebRTC disconnection logic here
      // Remove user from channel_members table
      
      dispatch({ type: 'SET_CURRENT_CHANNEL', payload: null });
      dispatch({ type: 'SET_CONNECTED', payload: false });
      dispatch({ type: 'SET_MUTED', payload: false });
      dispatch({ type: 'SET_DEAFENED', payload: false });
      
      console.log('Disconnected from voice channel');
    } catch (error) {
      console.error('Error disconnecting from voice channel:', error);
    }
  };

  // Toggle mute
  const toggleMute = () => {
    const newMuted = !state.isMuted;
    dispatch({ type: 'SET_MUTED', payload: newMuted });
    
    // TODO: Implement actual audio muting logic
    console.log(`Microphone ${newMuted ? 'muted' : 'unmuted'}`);
  };

  // Toggle deafen
  const toggleDeafen = () => {
    const newDeafened = !state.isDeafened;
    dispatch({ type: 'SET_DEAFENED', payload: newDeafened });
    
    // If deafening, also mute
    if (newDeafened) {
      dispatch({ type: 'SET_MUTED', payload: true });
    }
    
    // TODO: Implement actual audio deafening logic
    console.log(`Audio ${newDeafened ? 'deafened' : 'undeafened'}`);
  };

  const contextValue: VoiceContextType = {
    isConnected: state.isConnected,
    isMuted: state.isMuted,
    isDeafened: state.isDeafened,
    currentChannel: state.currentChannel,
    channelMembers: state.channelMembers,
    connect,
    disconnect,
    toggleMute,
    toggleDeafen
  };

  return (
    <VoiceContext.Provider value={contextValue}>
      {children}
    </VoiceContext.Provider>
  );
}

// Hook to use the context
export function useVoice() {
  const context = useContext(VoiceContext);
  if (!context) {
    throw new Error('useVoice must be used within a VoiceProvider');
  }
  return context;
}
