// TypeScript types for Stack Discord-like app

export type UserStatus = 'online' | 'away' | 'busy' | 'offline';
export type ChannelType = 'text' | 'voice';
export type MessageType = 'text' | 'image' | 'file' | 'system';
export type ServerRole = 'owner' | 'admin' | 'moderator' | 'member';

export interface User {
  id: string;
  username: string;
  display_name?: string;
  avatar_url?: string;
  bio?: string;
  status: UserStatus;
  last_seen: string;
  created_at: string;
  updated_at: string;
}

export interface Server {
  id: string;
  name: string;
  description?: string;
  icon_url?: string;
  owner_id: string;
  invite_code: string;
  created_at: string;
  updated_at: string;
}

export interface ServerMember {
  id: string;
  server_id: string;
  user_id: string;
  role: ServerRole;
  nickname?: string;
  joined_at: string;
  user?: User;
}

export interface Channel {
  id: string;
  server_id: string;
  name: string;
  description?: string;
  type: ChannelType;
  position: number;
  parent_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  channel_id: string;
  user_id: string;
  content?: string;
  type: MessageType;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  reply_to?: string;
  edited_at?: string;
  created_at: string;
  user?: User;
  reactions?: MessageReaction[];
  reply_message?: Message;
}

export interface MessageReaction {
  id: string;
  message_id: string;
  user_id: string;
  emoji: string;
  created_at: string;
  user?: User;
}

export interface ChannelMember {
  id: string;
  channel_id: string;
  user_id: string;
  joined_at: string;
  is_muted: boolean;
  is_deafened: boolean;
  user?: User;
}

export interface ServerInvite {
  id: string;
  server_id: string;
  inviter_id: string;
  code: string;
  max_uses?: number;
  uses: number;
  expires_at?: string;
  created_at: string;
  server?: Server;
  inviter?: User;
}

export interface TypingIndicator {
  id: string;
  channel_id: string;
  user_id: string;
  started_at: string;
  user?: User;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_id: string;
  status: UserStatus;
  last_activity: string;
  created_at: string;
}

// Extended types with relationships
export interface ServerWithChannels extends Server {
  channels: Channel[];
  members: ServerMember[];
  member_count: number;
  user_role?: ServerRole;
}

export interface ChannelWithMessages extends Channel {
  messages: Message[];
  members?: ChannelMember[];
  typing_users?: User[];
}

export interface MessageWithDetails extends Message {
  user: User;
  reactions: (MessageReaction & { user: User })[];
  reply_message?: MessageWithDetails;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  has_more: boolean;
  next_cursor?: string;
}

// Real-time event types
export interface RealtimeEvent<T = any> {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  record: T;
  old_record?: T;
}

export interface MessageEvent {
  type: 'new_message' | 'message_updated' | 'message_deleted';
  message: Message;
  channel_id: string;
}

export interface PresenceEvent {
  type: 'user_joined' | 'user_left' | 'status_changed';
  user_id: string;
  status?: UserStatus;
  channel_id?: string;
}

export interface TypingEvent {
  type: 'typing_start' | 'typing_stop';
  user_id: string;
  channel_id: string;
  user?: User;
}

// Form types
export interface CreateServerForm {
  name: string;
  description?: string;
  icon?: File;
}

export interface CreateChannelForm {
  name: string;
  description?: string;
  type: ChannelType;
  parent_id?: string;
}

export interface SendMessageForm {
  content?: string;
  file?: File;
  reply_to?: string;
}

export interface UpdateProfileForm {
  username?: string;
  display_name?: string;
  bio?: string;
  avatar?: File;
}

export interface JoinServerForm {
  invite_code: string;
}

// Context types
export interface AppContextType {
  user: User | null;
  servers: ServerWithChannels[];
  currentServer: ServerWithChannels | null;
  currentChannel: ChannelWithMessages | null;
  isLoading: boolean;
  error: string | null;
}

export interface ChatContextType {
  messages: MessageWithDetails[];
  typingUsers: User[];
  isLoadingMessages: boolean;
  hasMoreMessages: boolean;
  sendMessage: (content: string, file?: File, replyTo?: string) => Promise<void>;
  loadMoreMessages: () => Promise<void>;
  editMessage: (messageId: string, content: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => Promise<void>;
  removeReaction: (messageId: string, emoji: string) => Promise<void>;
}

export interface VoiceContextType {
  isConnected: boolean;
  isMuted: boolean;
  isDeafened: boolean;
  currentChannel: Channel | null;
  channelMembers: ChannelMember[];
  connect: (channelId: string) => Promise<void>;
  disconnect: () => Promise<void>;
  toggleMute: () => void;
  toggleDeafen: () => void;
}

// Utility types
export type DatabaseTables = 
  | 'users'
  | 'servers' 
  | 'server_members'
  | 'channels'
  | 'messages'
  | 'message_reactions'
  | 'channel_members'
  | 'server_invites'
  | 'typing_indicators'
  | 'user_sessions';

export type RealtimeChannel = `realtime:${DatabaseTables}`;

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

export class StackError extends Error {
  code: string;
  details?: any;

  constructor(code: string, message: string, details?: any) {
    super(message);
    this.name = 'StackError';
    this.code = code;
    this.details = details;
  }
}
