'use client';

import { useEffect, useRef } from 'react';
import { Hash } from 'lucide-react';
import { useApp } from '@/lib/contexts/AppContext';
import { useChat } from '@/lib/contexts/ChatContext';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { TypingIndicator } from './TypingIndicator';
import { LoadingSpinner } from '../ui/LoadingSpinner';

export function ChatArea() {
  const { state } = useApp();
  const { messages, typingUsers, isLoadingMessages } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (!state.currentChannel) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <Hash className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-400 mb-2">
            No channel selected
          </h2>
          <p className="text-gray-500">
            Select a channel from the sidebar to start chatting
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-900">
      {/* Channel Header */}
      <div className="h-12 border-b border-gray-700 flex items-center px-4">
        <Hash className="w-5 h-5 text-gray-400 mr-2" />
        <h1 className="font-semibold text-white">
          {state.currentChannel.name}
        </h1>
        {state.currentChannel.description && (
          <>
            <div className="w-px h-4 bg-gray-600 mx-3" />
            <p className="text-sm text-gray-400 truncate">
              {state.currentChannel.description}
            </p>
          </>
        )}
      </div>

      {/* Messages Area */}
      <div className="flex-1 flex flex-col min-h-0">
        {isLoadingMessages ? (
          <div className="flex-1 flex items-center justify-center">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <>
            <div className="flex-1 overflow-y-auto">
              <MessageList messages={messages} />
              <div ref={messagesEndRef} />
            </div>
            
            {/* Typing Indicator */}
            {typingUsers.length > 0 && (
              <div className="px-4 py-2">
                <TypingIndicator users={typingUsers} />
              </div>
            )}
          </>
        )}

        {/* Message Input */}
        <div className="p-4">
          <MessageInput />
        </div>
      </div>
    </div>
  );
}
