-- Clear Schema Script for Stack Discord-like App
-- This script will remove all tables, functions, triggers, and types created by schema.sql
--
-- ⚠️  WARNING: This will delete ALL data in the Stack application! ⚠️
--
-- Before running this script:
-- 1. Make sure you have backed up any important data
-- 2. Confirm you want to completely reset the Stack application
-- 3. This will NOT affect the Supabase auth system or other projects
--
-- What this script does:
-- ✅ Removes all Stack application tables and data
-- ✅ Removes all custom functions and triggers
-- ✅ Removes all custom types (enums)
-- ✅ Removes real-time subscriptions
-- ✅ Preserves Supabase auth system and core functionality
-- ✅ Optionally removes storage buckets (commented out by default)
--
-- After running this script, you can run schema.sql again to recreate everything.

-- Disable triggers temporarily to avoid issues during cleanup
SET session_replication_role = replica;

-- Remove real-time subscriptions first
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.messages;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.message_reactions;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.typing_indicators;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.channel_members;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.users;
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS public.server_members;

-- Drop triggers first (to avoid dependency issues)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
DROP TRIGGER IF EXISTS update_servers_updated_at ON public.servers;
DROP TRIGGER IF EXISTS update_channels_updated_at ON public.channels;
DROP TRIGGER IF EXISTS create_default_channels_trigger ON public.servers;
DROP TRIGGER IF EXISTS broadcast_message_trigger ON public.messages;

-- Drop all tables in dependency order (child tables first)
DROP TABLE IF EXISTS public.schema_version CASCADE;
DROP TABLE IF EXISTS public.user_sessions CASCADE;
DROP TABLE IF EXISTS public.typing_indicators CASCADE;
DROP TABLE IF EXISTS public.server_invites CASCADE;
DROP TABLE IF EXISTS public.channel_members CASCADE;
DROP TABLE IF EXISTS public.message_reactions CASCADE;
DROP TABLE IF EXISTS public.messages CASCADE;
DROP TABLE IF EXISTS public.channels CASCADE;
DROP TABLE IF EXISTS public.server_members CASCADE;
DROP TABLE IF EXISTS public.servers CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Drop all custom functions
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS public.update_user_last_seen(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.cleanup_typing_indicators() CASCADE;
DROP FUNCTION IF EXISTS public.get_server_members(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_user_servers(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_channel_messages(UUID, INTEGER, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.join_server_by_invite(VARCHAR) CASCADE;
DROP FUNCTION IF EXISTS public.create_default_channels() CASCADE;
DROP FUNCTION IF EXISTS public.broadcast_message() CASCADE;
DROP FUNCTION IF EXISTS public.update_user_presence(UUID, user_status) CASCADE;
DROP FUNCTION IF EXISTS public.cleanup_user_sessions() CASCADE;
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS public.user_status CASCADE;
DROP TYPE IF EXISTS public.channel_type CASCADE;
DROP TYPE IF EXISTS public.message_type CASCADE;
DROP TYPE IF EXISTS public.server_role CASCADE;

-- Re-enable triggers
SET session_replication_role = DEFAULT;

-- Clean up any remaining policies (in case they weren't dropped with tables)
-- Note: Policies are automatically dropped when tables are dropped, but this ensures cleanup

-- Optional: Clean up storage buckets (uncomment if you want to remove storage buckets too)
-- WARNING: This will delete all uploaded files!
-- DELETE FROM storage.objects WHERE bucket_id IN ('files', 'avatars');
-- DELETE FROM storage.buckets WHERE id IN ('files', 'avatars');

-- Verification queries to check if cleanup was successful
-- Uncomment these to verify the cleanup worked:

-- Check remaining tables
-- SELECT table_name FROM information_schema.tables 
-- WHERE table_schema = 'public' 
-- AND table_name NOT IN ('spatial_ref_sys') -- PostGIS table if present
-- ORDER BY table_name;

-- Check remaining functions
-- SELECT routine_name, routine_type 
-- FROM information_schema.routines 
-- WHERE routine_schema = 'public'
-- ORDER BY routine_name;

-- Check remaining types
-- SELECT typname FROM pg_type 
-- WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
-- AND typtype = 'e' -- enum types
-- ORDER BY typname;

-- Final verification and success message
DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    type_count INTEGER;
BEGIN
    -- Count remaining Stack-related objects
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN (
        'users', 'servers', 'server_members', 'channels', 'messages',
        'message_reactions', 'channel_members', 'server_invites',
        'typing_indicators', 'user_sessions', 'schema_version'
    );

    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name IN (
        'handle_new_user', 'update_user_last_seen', 'cleanup_typing_indicators',
        'get_server_members', 'get_user_servers', 'get_channel_messages',
        'join_server_by_invite', 'create_default_channels', 'broadcast_message',
        'update_user_presence', 'cleanup_user_sessions', 'update_updated_at_column'
    );

    SELECT COUNT(*) INTO type_count
    FROM pg_type
    WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
    AND typname IN ('user_status', 'channel_type', 'message_type', 'server_role');

    -- Report results
    RAISE NOTICE '=== STACK SCHEMA CLEANUP COMPLETED ===';
    RAISE NOTICE 'Tables remaining: % (should be 0)', table_count;
    RAISE NOTICE 'Functions remaining: % (should be 0)', function_count;
    RAISE NOTICE 'Custom types remaining: % (should be 0)', type_count;

    IF table_count = 0 AND function_count = 0 AND type_count = 0 THEN
        RAISE NOTICE '✅ SUCCESS: All Stack application objects have been removed!';
        RAISE NOTICE '✅ The auth system and other Supabase core functionality remain intact.';
        RAISE NOTICE '✅ You can now run schema.sql to recreate the Stack application.';
    ELSE
        RAISE NOTICE '⚠️  WARNING: Some objects may not have been removed completely.';
        RAISE NOTICE 'Please check the counts above and manually remove any remaining objects if needed.';
    END IF;
END $$;
