'use client';

import { useState } from 'react';
import { Hash, Volume2, Settings, UserPlus, ChevronDown, Plus } from 'lucide-react';
import { useApp } from '@/lib/contexts/AppContext';
import { Button } from '@/components/ui/button';
import { UserSettingsDialog } from './UserSettingsDialog';
import { ServerSettingsDialog } from './ServerSettingsDialog';
import { CreateChannelDialog } from './CreateChannelDialog';
import { cn } from '@/lib/utils';
import type { ChannelType } from '@/lib/types';

export function ChannelSidebar() {
  const { state, actions } = useApp();
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['text', 'voice']));
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);
  const [showServerSettingsDialog, setShowServerSettingsDialog] = useState(false);
  const [showCreateChannelDialog, setShowCreateChannelDialog] = useState(false);
  const [createChannelType, setCreateChannelType] = useState<ChannelType>('text');

  if (!state.currentServer) return null;

  const textChannels = state.currentServer.channels.filter(c => c.type === 'text');
  const voiceChannels = state.currentServer.channels.filter(c => c.type === 'voice');

  const handleChannelClick = (channelId: string) => {
    actions.setCurrentChannel(channelId);
  };

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(category)) {
      newExpanded.delete(category);
    } else {
      newExpanded.add(category);
    }
    setExpandedCategories(newExpanded);
  };

  const handleCreateChannel = (type: ChannelType) => {
    setCreateChannelType(type);
    setShowCreateChannelDialog(true);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Server Header */}
      <div className="p-4 border-b border-gray-600">
        <div className="flex items-center justify-between">
          <h2 className="font-semibold text-white truncate">
            {state.currentServer.name}
          </h2>
          <Button
            variant="ghost"
            size="icon"
            className="w-6 h-6"
            onClick={() => setShowServerSettingsDialog(true)}
          >
            <ChevronDown className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Channel List */}
      <div className="flex-1 overflow-y-auto p-2">
        {/* Text Channels */}
        <div className="mb-4">
          <Button
            variant="ghost"
            className="w-full justify-start p-1 h-auto text-xs font-semibold text-gray-400 hover:text-gray-300"
            onClick={() => toggleCategory('text')}
          >
            <ChevronDown 
              className={cn(
                "w-3 h-3 mr-1 transition-transform",
                !expandedCategories.has('text') && "-rotate-90"
              )} 
            />
            TEXT CHANNELS
            <Plus
              className="w-3 h-3 ml-auto opacity-0 group-hover:opacity-100 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleCreateChannel('text');
              }}
            />
          </Button>
          
          {expandedCategories.has('text') && (
            <div className="mt-1 space-y-0.5">
              {textChannels.map((channel) => (
                <Button
                  key={channel.id}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start p-2 h-auto text-gray-300 hover:text-white hover:bg-gray-600 rounded",
                    state.currentChannel?.id === channel.id && "bg-gray-600 text-white"
                  )}
                  onClick={() => handleChannelClick(channel.id)}
                >
                  <Hash className="w-4 h-4 mr-2 text-gray-400" />
                  <span className="truncate">{channel.name}</span>
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* Voice Channels */}
        <div className="mb-4">
          <Button
            variant="ghost"
            className="w-full justify-start p-1 h-auto text-xs font-semibold text-gray-400 hover:text-gray-300"
            onClick={() => toggleCategory('voice')}
          >
            <ChevronDown 
              className={cn(
                "w-3 h-3 mr-1 transition-transform",
                !expandedCategories.has('voice') && "-rotate-90"
              )} 
            />
            VOICE CHANNELS
            <Plus
              className="w-3 h-3 ml-auto opacity-0 group-hover:opacity-100 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleCreateChannel('voice');
              }}
            />
          </Button>
          
          {expandedCategories.has('voice') && (
            <div className="mt-1 space-y-0.5">
              {voiceChannels.map((channel) => (
                <Button
                  key={channel.id}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start p-2 h-auto text-gray-300 hover:text-white hover:bg-gray-600 rounded",
                    state.currentChannel?.id === channel.id && "bg-gray-600 text-white"
                  )}
                  onClick={() => handleChannelClick(channel.id)}
                >
                  <Volume2 className="w-4 h-4 mr-2 text-gray-400" />
                  <span className="truncate">{channel.name}</span>
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* User Area */}
      <div className="p-2 border-t border-gray-600">
        <div className="flex items-center space-x-2 p-2 rounded hover:bg-gray-600 cursor-pointer">
          <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white text-sm font-semibold">
            {state.user?.display_name?.charAt(0) || state.user?.username?.charAt(0) || 'U'}
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-white truncate">
              {state.user?.display_name || state.user?.username}
            </div>
            <div className="text-xs text-gray-400 truncate">
              #{state.user?.username}
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="w-6 h-6"
            onClick={() => setShowSettingsDialog(true)}
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Settings Dialogs */}
      <UserSettingsDialog
        open={showSettingsDialog}
        onOpenChange={setShowSettingsDialog}
      />
      <ServerSettingsDialog
        open={showServerSettingsDialog}
        onOpenChange={setShowServerSettingsDialog}
      />
      <CreateChannelDialog
        open={showCreateChannelDialog}
        onOpenChange={setShowCreateChannelDialog}
        defaultType={createChannelType}
      />
    </div>
  );
}
