# Database Setup Guide for Stack

This guide will help you set up the complete database schema for the Stack Discord-like chat application.

## Prerequisites

1. A Supabase account and project
2. Access to the Supabase SQL Editor

## Step 1: Run the Schema

This will create:
- All necessary tables with proper relationships
- Row Level Security (RLS) policies
- Database functions and triggers
- Indexes for performance
- Real-time subscriptions

## Step 2: Set Up Storage (Optional)

For file uploads and avatars, you'll need to create storage buckets:

### Create Buckets

1. Go to Storage in your Supabase dashboard
2. Create a new bucket named `files`
3. Create another bucket named `avatars`

### Set Up Bucket Policies

For the `files` bucket, add this policy:

```sql
-- Allow authenticated users to upload files
CREATE POLICY "Users can upload files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'files' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow public access to files
CREATE POLICY "Files are publicly accessible" ON storage.objects
FOR SELECT USING (bucket_id = 'files');

-- Allow users to delete their own files
CREATE POLICY "Users can delete own files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'files' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

For the `avatars` bucket, add this policy:

```sql
-- Allow authenticated users to upload avatars
CREATE POLICY "Users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow public access to avatars
CREATE POLICY "Avatars are publicly accessible" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- Allow users to update their own avatars
CREATE POLICY "Users can update own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow users to delete their own avatars
CREATE POLICY "Users can delete own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

## Step 3: Configure Real-time

The schema automatically enables real-time for the necessary tables. Make sure Real-time is enabled in your Supabase project:

1. Go to Settings > API
2. Ensure Real-time is enabled
3. The following tables are configured for real-time:
   - `messages`
   - `message_reactions`
   - `typing_indicators`
   - `channel_members`
   - `users`
   - `server_members`

## Step 4: Test the Setup

After running the schema, you should have:

### Tables Created
- `users` - Extended user profiles
- `servers` - Discord-like servers
- `server_members` - User-server relationships
- `channels` - Text and voice channels
- `messages` - Chat messages
- `message_reactions` - Message reactions
- `channel_members` - Voice channel members
- `server_invites` - Server invitations
- `typing_indicators` - Real-time typing
- `user_sessions` - User presence

### Functions Available
- `handle_new_user()` - Automatically creates user profile on signup
- `update_user_last_seen()` - Updates user activity
- `cleanup_typing_indicators()` - Removes old typing indicators
- `get_server_members()` - Gets server members with user details
- `get_user_servers()` - Gets user's servers
- `get_channel_messages()` - Gets channel messages with pagination
- `join_server_by_invite()` - Joins server using invite code
- `create_default_channels()` - Creates default channels for new servers
- `update_user_presence()` - Updates user online status

### Security
- All tables have Row Level Security (RLS) enabled
- Policies ensure users can only access data they should see
- Server members can only see channels in servers they belong to
- Users can only send messages to channels they have access to

## Step 5: Environment Variables

Make sure your `.env.local` file has the correct values:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Troubleshooting

### Common Issues

**RLS Policies Too Restrictive**: If you can't see data, check the RLS policies

The application will automatically handle:
- User registration and profile creation
- Real-time message delivery
- Typing indicators
- User presence updates
- File uploads (if storage is configured)
- Server and channel management
