'use client';

import { useState } from 'react';
import { Plus, Users, MessageSquare } from 'lucide-react';
import { useApp } from '@/lib/contexts/AppContext';
import { Button } from '@/components/ui/button';
import { CreateServerDialog } from './CreateServerDialog';
import { JoinServerDialog } from './JoinServerDialog';

export function WelcomeScreen() {
  const { state } = useApp();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showJoinDialog, setShowJoinDialog] = useState(false);

  return (
    <div className="flex-1 flex items-center justify-center bg-gray-900">
      <div className="text-center max-w-md mx-auto p-8">
        <div className="mb-8">
          <MessageSquare className="w-24 h-24 text-blue-500 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-white mb-4">
            Welcome to Stack
          </h1>
          <p className="text-gray-400 text-lg">
            Connect with your community and start chatting in real-time
          </p>
        </div>

        {state.servers.length === 0 ? (
          <div className="space-y-4">
            <p className="text-gray-300 mb-6">
              Get started by creating your first server or joining an existing one
            </p>
            
            <div className="space-y-3">
              <Button
                onClick={() => setShowCreateDialog(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                size="lg"
              >
                <Plus className="w-5 h-5 mr-2" />
                Create a Server
              </Button>
              
              <Button
                onClick={() => setShowJoinDialog(true)}
                variant="outline"
                className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                size="lg"
              >
                <Users className="w-5 h-5 mr-2" />
                Join a Server
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-gray-300 mb-6">
              Select a server from the sidebar to start chatting
            </p>
            
            <div className="grid grid-cols-1 gap-3">
              {state.servers.slice(0, 3).map((server) => (
                <Button
                  key={server.id}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800 justify-start"
                  onClick={() => {
                    // This will be handled by the server sidebar
                  }}
                >
                  <div className="w-8 h-8 rounded bg-blue-600 flex items-center justify-center text-white font-semibold mr-3">
                    {server.name.charAt(0).toUpperCase()}
                  </div>
                  {server.name}
                </Button>
              ))}
            </div>
            
            {state.servers.length > 3 && (
              <p className="text-sm text-gray-500 mt-4">
                And {state.servers.length - 3} more servers...
              </p>
            )}
          </div>
        )}

        {/* Dialogs */}
        <CreateServerDialog 
          open={showCreateDialog} 
          onOpenChange={setShowCreateDialog} 
        />
        <JoinServerDialog 
          open={showJoinDialog} 
          onOpenChange={setShowJoinDialog} 
        />
      </div>
    </div>
  );
}
