'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { createClient } from '../supabase/client';
import { userOperations, serverOperations } from '../supabase/database';
import { useRealtimePresence, useRealtimeStatus } from '../hooks/useRealtime';
import type { 
  User, 
  ServerWithChannels, 
  ChannelWithMessages,
  AppContextType 
} from '../types';

// Action types
type AppAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_SERVERS'; payload: ServerWithChannels[] }
  | { type: 'SET_CURRENT_SERVER'; payload: ServerWithChannels | null }
  | { type: 'SET_CURRENT_CHANNEL'; payload: ChannelWithMessages | null }
  | { type: 'UPDATE_USER_STATUS'; payload: { userId: string; status: User['status'] } }
  | { type: 'ADD_SERVER'; payload: ServerWithChannels }
  | { type: 'REMOVE_SERVER'; payload: string }
  | { type: 'UPDATE_SERVER'; payload: ServerWithChannels };

// Initial state
const initialState: AppContextType = {
  user: null,
  servers: [],
  currentServer: null,
  currentChannel: null,
  isLoading: true,
  error: null
};

// Reducer
function appReducer(state: AppContextType, action: AppAction): AppContextType {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    case 'SET_SERVERS':
      return { ...state, servers: action.payload };
    
    case 'SET_CURRENT_SERVER':
      return { ...state, currentServer: action.payload };
    
    case 'SET_CURRENT_CHANNEL':
      return { ...state, currentChannel: action.payload };
    
    case 'UPDATE_USER_STATUS':
      return {
        ...state,
        servers: state.servers.map(server => ({
          ...server,
          members: server.members.map(member => 
            member.user_id === action.payload.userId
              ? { ...member, user: member.user ? { ...member.user, status: action.payload.status } : undefined }
              : member
          )
        }))
      };
    
    case 'ADD_SERVER':
      return {
        ...state,
        servers: [...state.servers, action.payload]
      };
    
    case 'REMOVE_SERVER':
      return {
        ...state,
        servers: state.servers.filter(server => server.id !== action.payload),
        currentServer: state.currentServer?.id === action.payload ? null : state.currentServer
      };
    
    case 'UPDATE_SERVER':
      return {
        ...state,
        servers: state.servers.map(server => 
          server.id === action.payload.id ? action.payload : server
        ),
        currentServer: state.currentServer?.id === action.payload.id ? action.payload : state.currentServer
      };
    
    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppContextType;
  dispatch: React.Dispatch<AppAction>;
  actions: {
    loadUserData: () => Promise<void>;
    setCurrentServer: (serverId: string) => void;
    setCurrentChannel: (channelId: string) => void;
    joinServer: (inviteCode: string) => Promise<{ success: boolean; error?: string }>;
    leaveServer: (serverId: string) => Promise<void>;
    createServer: (name: string, description?: string) => Promise<ServerWithChannels>;
    updateUserStatus: (status: User['status']) => Promise<void>;
  };
} | null>(null);

// Provider component
interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  const supabase = createClient();

  // Real-time subscriptions
  useRealtimeStatus();
  useRealtimePresence(
    state.servers.map(s => s.id),
    (userId, status) => {
      dispatch({ type: 'UPDATE_USER_STATUS', payload: { userId, status } });
    }
  );

  // Load user data
  const loadUserData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      // Get current user
      const user = await userOperations.getCurrentUser();
      if (!user) {
        dispatch({ type: 'SET_USER', payload: null });
        dispatch({ type: 'SET_LOADING', payload: false });
        return;
      }

      dispatch({ type: 'SET_USER', payload: user });

      // Get user's servers
      const servers = await serverOperations.getUserServers();
      dispatch({ type: 'SET_SERVERS', payload: servers });

      // Update user presence
      await userOperations.updateStatus('online');

    } catch (error) {
      console.error('Error loading user data:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load user data' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Set current server
  const setCurrentServer = (serverId: string) => {
    const server = state.servers.find(s => s.id === serverId);
    if (server) {
      dispatch({ type: 'SET_CURRENT_SERVER', payload: server });
      // Auto-select first text channel
      const firstTextChannel = server.channels.find(c => c.type === 'text');
      if (firstTextChannel) {
        setCurrentChannel(firstTextChannel.id);
      }
    }
  };

  // Set current channel
  const setCurrentChannel = (channelId: string) => {
    if (!state.currentServer) return;
    
    const channel = state.currentServer.channels.find(c => c.id === channelId);
    if (channel) {
      dispatch({ 
        type: 'SET_CURRENT_CHANNEL', 
        payload: { ...channel, messages: [], typing_users: [] } 
      });
    }
  };

  // Join server by invite
  const joinServer = async (inviteCode: string) => {
    try {
      const result = await serverOperations.joinServerByInvite(inviteCode);
      
      if (result.success && result.data) {
        // Reload servers to include the new one
        await loadUserData();
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error joining server:', error);
      return { success: false, error: 'Failed to join server' };
    }
  };

  // Leave server
  const leaveServer = async (serverId: string) => {
    try {
      await serverOperations.leaveServer(serverId);
      dispatch({ type: 'REMOVE_SERVER', payload: serverId });
    } catch (error) {
      console.error('Error leaving server:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to leave server' });
    }
  };

  // Create server
  const createServer = async (name: string, description?: string) => {
    try {
      const server = await serverOperations.createServer(name, description);
      const serverWithChannels: ServerWithChannels = {
        ...server,
        channels: [],
        members: [],
        member_count: 1,
        user_role: 'owner'
      };
      
      dispatch({ type: 'ADD_SERVER', payload: serverWithChannels });
      
      // Reload to get the created channels
      await loadUserData();
      
      return serverWithChannels;
    } catch (error) {
      console.error('Error creating server:', error);
      throw error;
    }
  };

  // Update user status
  const updateUserStatus = async (status: User['status']) => {
    try {
      await userOperations.updateStatus(status);
      if (state.user) {
        dispatch({ 
          type: 'SET_USER', 
          payload: { ...state.user, status } 
        });
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  // Auth state listener
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          await loadUserData();
        } else if (event === 'SIGNED_OUT') {
          dispatch({ type: 'SET_USER', payload: null });
          dispatch({ type: 'SET_SERVERS', payload: [] });
          dispatch({ type: 'SET_CURRENT_SERVER', payload: null });
          dispatch({ type: 'SET_CURRENT_CHANNEL', payload: null });
        }
      }
    );

    // Initial load
    loadUserData();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.user) {
        userOperations.updateStatus('offline').catch(console.error);
      }
    };
  }, [state.user]);

  const actions = {
    loadUserData,
    setCurrentServer,
    setCurrentChannel,
    joinServer,
    leaveServer,
    createServer,
    updateUserStatus
  };

  return (
    <AppContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook to use the context
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
