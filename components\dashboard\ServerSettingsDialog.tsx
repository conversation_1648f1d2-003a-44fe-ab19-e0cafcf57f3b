'use client';

import { useState } from 'react';
import { useApp } from '@/lib/contexts/AppContext';
import { serverOperations } from '@/lib/supabase/database';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { Co<PERSON>, Users, Settings, Trash2, UserPlus } from 'lucide-react';

interface ServerSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ServerSettingsDialog({ open, onOpenChange }: ServerSettingsDialogProps) {
  const { state, actions } = useApp();
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'invites'>('overview');
  const [inviteCode, setInviteCode] = useState('');
  const [isCreatingInvite, setIsCreatingInvite] = useState(false);
  const [isLeavingServer, setIsLeavingServer] = useState(false);

  if (!state.currentServer) return null;

  const isOwner = state.currentServer.user_role === 'owner';
  const isAdmin = ['owner', 'admin'].includes(state.currentServer.user_role || '');

  const handleCreateInvite = async () => {
    if (!state.currentServer) return;

    setIsCreatingInvite(true);
    try {
      const code = await serverOperations.createInvite(state.currentServer.id);
      setInviteCode(code);
    } catch (error) {
      console.error('Error creating invite:', error);
    } finally {
      setIsCreatingInvite(false);
    }
  };

  const handleCopyInvite = () => {
    if (inviteCode) {
      navigator.clipboard.writeText(inviteCode);
      // You could add a toast notification here
    }
  };

  const handleLeaveServer = async () => {
    if (!state.currentServer || isOwner) return;

    setIsLeavingServer(true);
    try {
      await actions.leaveServer(state.currentServer.id);
      onOpenChange(false);
    } catch (error) {
      console.error('Error leaving server:', error);
    } finally {
      setIsLeavingServer(false);
    }
  };

  const TabButton = ({ tab, label, icon: Icon }: { tab: string; label: string; icon: any }) => (
    <Button
      variant={activeTab === tab ? 'default' : 'ghost'}
      className={`justify-start ${activeTab === tab ? 'bg-blue-600' : 'text-gray-400 hover:text-white'}`}
      onClick={() => setActiveTab(tab as any)}
    >
      <Icon className="w-4 h-4 mr-2" />
      {label}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] bg-gray-800 border-gray-700 max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-white">{state.currentServer.name} Settings</DialogTitle>
          <DialogDescription className="text-gray-400">
            Manage your server settings and members.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex h-[500px]">
          {/* Sidebar */}
          <div className="w-48 border-r border-gray-700 pr-4">
            <div className="space-y-1">
              <TabButton tab="overview" label="Overview" icon={Settings} />
              <TabButton tab="members" label="Members" icon={Users} />
              <TabButton tab="invites" label="Invites" icon={UserPlus} />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 pl-4 overflow-y-auto">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Server Overview</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <Label className="text-white">Server Name</Label>
                      <Input
                        value={state.currentServer.name}
                        disabled
                        className="bg-gray-700 border-gray-600 text-white mt-1"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-white">Server ID</Label>
                      <Input
                        value={state.currentServer.id}
                        disabled
                        className="bg-gray-700 border-gray-600 text-white mt-1 font-mono text-xs"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-white">Created</Label>
                      <Input
                        value={new Date(state.currentServer.created_at).toLocaleDateString()}
                        disabled
                        className="bg-gray-700 border-gray-600 text-white mt-1"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-white">Members</Label>
                      <Input
                        value={`${state.currentServer.members.length} members`}
                        disabled
                        className="bg-gray-700 border-gray-600 text-white mt-1"
                      />
                    </div>
                  </div>
                </div>

                {!isOwner && (
                  <div className="border-t border-gray-700 pt-6">
                    <h4 className="text-red-400 font-semibold mb-2">Danger Zone</h4>
                    <Button
                      variant="outline"
                      className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                      onClick={handleLeaveServer}
                      disabled={isLeavingServer}
                    >
                      {isLeavingServer ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Leaving...
                        </>
                      ) : (
                        <>
                          <Trash2 className="w-4 h-4 mr-2" />
                          Leave Server
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'members' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">Server Members</h3>
                
                <div className="space-y-2">
                  {state.currentServer.members.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white text-sm font-semibold">
                          {(member.user?.display_name || member.user?.username || 'U').charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="text-white font-medium">
                            {member.nickname || member.user?.display_name || member.user?.username || 'Unknown'}
                          </div>
                          <div className="text-xs text-gray-400 capitalize">
                            {member.role}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          member.user?.status === 'online' ? 'bg-green-500' :
                          member.user?.status === 'away' ? 'bg-yellow-500' :
                          member.user?.status === 'busy' ? 'bg-red-500' : 'bg-gray-500'
                        }`} />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'invites' && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white">Server Invites</h3>
                
                <div className="space-y-4">
                  <div>
                    <Button
                      onClick={handleCreateInvite}
                      disabled={isCreatingInvite}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      {isCreatingInvite ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <UserPlus className="w-4 h-4 mr-2" />
                          Create Invite
                        </>
                      )}
                    </Button>
                  </div>

                  {inviteCode && (
                    <div className="p-4 bg-gray-700 rounded-lg">
                      <Label className="text-white">Invite Code</Label>
                      <div className="flex items-center space-x-2 mt-2">
                        <Input
                          value={inviteCode}
                          disabled
                          className="bg-gray-600 border-gray-500 text-white font-mono"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={handleCopyInvite}
                          className="border-gray-600 text-gray-300 hover:bg-gray-600"
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-gray-400 mt-2">
                        Share this code with others to invite them to the server
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
