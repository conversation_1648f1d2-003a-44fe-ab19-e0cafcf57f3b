'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { AppProvider } from '@/lib/contexts/AppContext';
import { ChatProvider } from '@/lib/contexts/ChatContext';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';

export default function DashboardPage() {
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/');
      }
    };

    checkAuth();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (event === 'SIGNED_OUT' || !session) {
          router.push('/');
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [router, supabase]);

  return (
    <AppProvider>
      <ChatProvider>
        <DashboardLayout />
      </ChatProvider>
    </AppProvider>
  );
}
