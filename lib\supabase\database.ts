// Database utilities for Stack app
import { createClient } from './client';
import type { 
  User, 
  Server, 
  Channel, 
  Message, 
  ServerMember, 
  MessageWithDetails,
  ServerWithChannels,
  ChannelWithMessages,
  ApiResponse,
  PaginatedResponse
} from '../types';

const supabase = createClient();

// User operations
export const userOperations = {
  async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) throw error;
    return data;
  },

  async updateProfile(updates: Partial<User>): Promise<User> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async updateStatus(status: User['status']): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase.rpc('update_user_presence', {
      user_uuid: user.id,
      new_status: status
    });

    if (error) throw error;
  },

  async searchUsers(query: string): Promise<User[]> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .or(`username.ilike.%${query}%,display_name.ilike.%${query}%`)
      .limit(10);

    if (error) throw error;
    return data || [];
  }
};

// Server operations
export const serverOperations = {
  async getUserServers(): Promise<ServerWithChannels[]> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data, error } = await supabase.rpc('get_user_servers', {
      user_uuid: user.id
    });

    if (error) throw error;

    // Get channels for each server
    const serversWithChannels = await Promise.all(
      (data || []).map(async (server: any) => {
        const { data: channels } = await supabase
          .from('channels')
          .select('*')
          .eq('server_id', server.id)
          .order('position');

        return {
          ...server,
          channels: channels || [],
          user_role: server.role
        };
      })
    );

    return serversWithChannels;
  },

  async createServer(name: string, description?: string): Promise<Server> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data, error } = await supabase
      .from('servers')
      .insert({
        name,
        description,
        owner_id: user.id
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async joinServerByInvite(inviteCode: string): Promise<ApiResponse<{ server_id: string }>> {
    const { data, error } = await supabase.rpc('join_server_by_invite', {
      invite_code_param: inviteCode
    });

    if (error) throw error;

    const result = data?.[0];
    return {
      success: result?.success || false,
      data: result?.success ? { server_id: result.server_id } : undefined,
      error: result?.message
    };
  },

  async getServerMembers(serverId: string): Promise<ServerMember[]> {
    const { data, error } = await supabase.rpc('get_server_members', {
      server_uuid: serverId
    });

    if (error) throw error;
    return data || [];
  },

  async leaveServer(serverId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('server_members')
      .delete()
      .eq('server_id', serverId)
      .eq('user_id', user.id);

    if (error) throw error;
  },

  async createInvite(serverId: string, maxUses?: number, expiresAt?: string): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data, error } = await supabase
      .from('server_invites')
      .insert({
        server_id: serverId,
        inviter_id: user.id,
        max_uses: maxUses,
        expires_at: expiresAt
      })
      .select('code')
      .single();

    if (error) throw error;
    return data.code;
  }
};

// Channel operations
export const channelOperations = {
  async getChannelMessages(
    channelId: string, 
    limit = 50, 
    beforeMessage?: string
  ): Promise<MessageWithDetails[]> {
    const { data, error } = await supabase.rpc('get_channel_messages', {
      channel_uuid: channelId,
      message_limit: limit,
      before_message: beforeMessage || null
    });

    if (error) throw error;

    // Get reactions for each message
    const messagesWithReactions = await Promise.all(
      (data || []).map(async (message: any) => {
        const { data: reactions } = await supabase
          .from('message_reactions')
          .select(`
            *,
            user:users(*)
          `)
          .eq('message_id', message.id);

        return {
          ...message,
          user: {
            id: message.user_id,
            username: message.username,
            display_name: message.display_name,
            avatar_url: message.avatar_url
          },
          reactions: reactions || []
        };
      })
    );

    return messagesWithReactions;
  },

  async sendMessage(
    channelId: string, 
    content?: string, 
    fileUrl?: string, 
    fileName?: string,
    fileSize?: number,
    replyTo?: string
  ): Promise<Message> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data, error } = await supabase
      .from('messages')
      .insert({
        channel_id: channelId,
        user_id: user.id,
        content,
        file_url: fileUrl,
        file_name: fileName,
        file_size: fileSize,
        reply_to: replyTo,
        type: fileUrl ? (fileName?.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? 'image' : 'file') : 'text'
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async editMessage(messageId: string, content: string): Promise<Message> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { data, error } = await supabase
      .from('messages')
      .update({ 
        content, 
        edited_at: new Date().toISOString() 
      })
      .eq('id', messageId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async deleteMessage(messageId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('messages')
      .delete()
      .eq('id', messageId)
      .eq('user_id', user.id);

    if (error) throw error;
  },

  async addReaction(messageId: string, emoji: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('message_reactions')
      .insert({
        message_id: messageId,
        user_id: user.id,
        emoji
      });

    if (error && !error.message.includes('duplicate')) throw error;
  },

  async removeReaction(messageId: string, emoji: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const { error } = await supabase
      .from('message_reactions')
      .delete()
      .eq('message_id', messageId)
      .eq('user_id', user.id)
      .eq('emoji', emoji);

    if (error) throw error;
  },

  async createChannel(
    serverId: string, 
    name: string, 
    type: Channel['type'] = 'text',
    description?: string
  ): Promise<Channel> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    // Get next position
    const { data: channels } = await supabase
      .from('channels')
      .select('position')
      .eq('server_id', serverId)
      .order('position', { ascending: false })
      .limit(1);

    const nextPosition = (channels?.[0]?.position || 0) + 1;

    const { data, error } = await supabase
      .from('channels')
      .insert({
        server_id: serverId,
        name,
        type,
        description,
        position: nextPosition
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// File upload operations
export const fileOperations = {
  async uploadFile(file: File, bucket = 'files'): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('Not authenticated');

    const fileExt = file.name.split('.').pop();
    const fileName = `${user.id}/${Date.now()}.${fileExt}`;

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(fileName, file);

    if (error) throw error;

    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(fileName);

    return publicUrl;
  },

  async uploadAvatar(file: File): Promise<string> {
    return this.uploadFile(file, 'avatars');
  }
};
