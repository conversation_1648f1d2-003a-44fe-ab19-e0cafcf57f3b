'use client';

import { Users } from 'lucide-react';
import { useApp } from '@/lib/contexts/AppContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

export function UserArea() {
  const { state } = useApp();

  if (!state.currentServer) {
    return (
      <div className="p-4 text-center text-gray-500">
        <Users className="w-8 h-8 mx-auto mb-2" />
        <p className="text-sm">No server selected</p>
      </div>
    );
  }

  const onlineMembers = state.currentServer.members.filter(
    member => member.user?.status === 'online'
  );
  const offlineMembers = state.currentServer.members.filter(
    member => member.user?.status !== 'online'
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const MemberItem = ({ member }: { member: any }) => (
    <div className="flex items-center space-x-3 p-2 rounded hover:bg-gray-700 cursor-pointer">
      <div className="relative">
        <Avatar className="w-8 h-8">
          <AvatarImage src={member.user?.avatar_url} alt={member.user?.username} />
          <AvatarFallback className="bg-blue-600 text-white text-sm">
            {(member.user?.display_name || member.user?.username || 'U').charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className={cn(
          "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-gray-800",
          getStatusColor(member.user?.status || 'offline')
        )} />
      </div>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-white truncate">
          {member.nickname || member.user?.display_name || member.user?.username || 'Unknown'}
        </div>
        {member.role !== 'member' && (
          <div className="text-xs text-gray-400 capitalize">
            {member.role}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <h3 className="font-semibold text-white flex items-center">
          <Users className="w-4 h-4 mr-2" />
          Members — {state.currentServer.members.length}
        </h3>
      </div>

      {/* Member List */}
      <div className="flex-1 overflow-y-auto p-2">
        {/* Online Members */}
        {onlineMembers.length > 0 && (
          <div className="mb-4">
            <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2 px-2">
              Online — {onlineMembers.length}
            </h4>
            <div className="space-y-1">
              {onlineMembers.map((member) => (
                <MemberItem key={member.id} member={member} />
              ))}
            </div>
          </div>
        )}

        {/* Offline Members */}
        {offlineMembers.length > 0 && (
          <div>
            <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2 px-2">
              Offline — {offlineMembers.length}
            </h4>
            <div className="space-y-1">
              {offlineMembers.map((member) => (
                <MemberItem key={member.id} member={member} />
              ))}
            </div>
          </div>
        )}

        {state.currentServer.members.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            <p className="text-sm">No members found</p>
          </div>
        )}
      </div>
    </div>
  );
}
